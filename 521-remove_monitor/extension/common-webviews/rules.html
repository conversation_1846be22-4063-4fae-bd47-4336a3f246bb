<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Rules Editor</title>
    <meta property="csp-nonce" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <script type="module" crossorigin src="./assets/rules-BqgkooOl.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-AffdR7--.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BpU1o6o4.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DVt24OaC.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-K-zrKZyw.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CB88N7dm.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-LQjgzAXk.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-DXXiLgz5.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-Bv_1VsFe.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CB_5BS9R.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-D6uePTe3.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-IcL3sG2L.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/index-CnLsnTY6.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/index-C5DcjNTh.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DXcDoFHp.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-jvQuAtrB.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-BXNDUf24.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DLmRCR9z.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BBAM6A1q.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-eRXt2w4J.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-DVF13ARD.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-left-DyB-JMbr.js" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-B6vv3aGc.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
    <link rel="stylesheet" crossorigin href="./assets/rules-D-OA6lpr.css" nonce="nonce-F+aPZaLdmKrPB/Y4mhFG/w==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
