import{C as b,J as h,H as J,t as o,M as a,Q as O,a3 as gt,ab as D,P as $,b as p,W as et,R as ft,A as dt,B as yt,m,ak as wt,aE as pt,_ as t,Y as c,X as Z,K as tt,$ as kt,D as mt,L as $t,I as ht,G as bt,al as At,F as qt,az as Bt}from"./SpinnerAugment-AffdR7--.js";import{c as B,W as H}from"./IconButtonAugment-DVt24OaC.js";import{aJ as lt}from"./AugmentMessage-GSkxqNfK.js";import{b as Ct,a as xt}from"./BaseTextInput-IcL3sG2L.js";import{C as Mt,S as Rt}from"./folder-opened-a0bLStc3.js";import{M as It}from"./message-broker-Bv_1VsFe.js";import{s as St}from"./chat-model-context-LQjgzAXk.js";import{M as Wt}from"./index-ZbZe59K6.js";import"./CalloutAugment-4ajbBCm_.js";import"./CardAugment-CB88N7dm.js";import"./index-CnLsnTY6.js";import"./async-messaging-DXXiLgz5.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CB_5BS9R.js";import"./isObjectLike-D6uePTe3.js";import"./index-C5DcjNTh.js";import"./diff-operations-zlr5gNTh.js";import"./svelte-component-BzMfvILK.js";import"./Filespan-CLvGlhI3.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-637P_Cby.js";import"./OpenFileButton-DLmRCR9z.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-DXcDoFHp.js";import"./ra-diff-ops-model-jvQuAtrB.js";import"./TextAreaAugment-BXNDUf24.js";import"./ButtonAugment-K-zrKZyw.js";import"./CollapseButtonAugment-DgGSnbBS.js";import"./user-BlpcIo5U.js";import"./MaterialIcon-DgNf9Yde.js";import"./CopyButton-Cx19Z4lO.js";import"./ellipsis-BHLqUIzX.js";import"./IconFilePath-Bz0iFBtq.js";import"./LanguageIcon-CVD96Tjf.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-BBAM6A1q.js";import"./index-oHUUsc-1.js";import"./augment-logo-Dea9S5F6.js";import"./pen-to-square-ChHviosp.js";import"./check-CEaHRvsZ.js";var Dt=h('<div class="header svelte-1894wv4"> </div>'),_t=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(N,C){let i=b(C,"selected",12,null),x=b(C,"question",8,null);function v(w){i(w)}var e=_t(),d=o(e),_=w=>{var P=Dt(),G=o(P);O(()=>et(G,x())),p(w,P)};J(d,w=>{x()&&w(_)});var M=a(d,2),g=o(M);let f;var r=a(g,2);let A;var y=a(r,2);let s;var R=a(y,2);let V;var z=a(R,2);let X;var L=a(z,2);let Q;var j=a(L,2);let Y;O((w,P,G,at,st,l,u)=>{f=D(g,1,"button large svelte-1894wv4",null,f,w),A=D(r,1,"button medium svelte-1894wv4",null,A,P),s=D(y,1,"button small svelte-1894wv4",null,s,G),V=D(R,1,"button equal svelte-1894wv4",null,V,at),X=D(z,1,"button small svelte-1894wv4",null,X,st),Q=D(L,1,"button medium svelte-1894wv4",null,Q,l),Y=D(j,1,"button large svelte-1894wv4",null,Y,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),$("click",g,()=>v("A3")),$("click",r,()=>v("A2")),$("click",y,()=>v("A1")),$("click",R,()=>v("=")),$("click",z,()=>v("B1")),$("click",L,()=>v("B2")),$("click",j,()=>v("B3")),p(N,e)}var zt=h('<div class="question svelte-1i0f73l"> </div>'),Lt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Pt=h('<button class="button svelte-2k5n"> </button>'),Et=h("<div> </div>"),Ft=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Ot=h("<!> <!> <!> <!> <!> <!>",1),Ht=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Jt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Nt(N,C){dt(C,!1);const i=m(),x=m(),v=m();let e=b(C,"inputData",8);const d=yt();let _=new Mt(new It(B),B,new Rt);St(_);let M=m(null),g=m(null),f=null,r=m(null),A=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function V(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(M)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t(A)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===H.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===H.chatStreamDone&&c(R,!0)})}),Z(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),Z(()=>(t(s),tt(e())),()=>{c(x,t(s).a!==null?t(s).a:e().data.a.response)}),Z(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),Z(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var z=Jt(),X=o(z),L=a(o(X),2);lt(L,{get markdown(){return tt(e()),$t(()=>e().data.a.message)}});var Q=a(L,4),j=o(Q),Y=a(o(j),2);lt(Y,{get markdown(){return t(x)}});var w=a(j,4),P=a(o(w),2);lt(P,{get markdown(){return t(v)}});var G=a(Q,4),at=l=>{var u=Ot(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(M)},set selected(n){c(M,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let K=b(k,"isChecked",12,!1),I=b(k,"question",8,null);var q=Ft(),S=o(q),E=W=>{var U=Et(),it=o(U);O(()=>et(it,I())),p(W,U)};J(S,W=>{I()&&W(E)});var F=a(S,2),T=o(F);xt(T,K),p(n,q)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let K=b(k,"value",12,""),I=b(k,"question",8,null),q=b(k,"placeholder",8,"");var S=Lt(),E=o(S),F=W=>{var U=zt(),it=o(U);O(()=>et(it,I())),p(W,U)};J(E,W=>{I()&&W(F)});var T=a(E,2);O(()=>ft(T,"placeholder",q())),Ct(T,K),p(n,S)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t(A)},set value(n){c(A,n)},$$legacy:!0}),function(n,k){let K=b(k,"label",8,"Submit"),I=b(k,"onClick",8);var q=Pt(),S=o(q);O(()=>et(S,K())),$("click",q,function(...E){var F;(F=I())==null||F.apply(this,E)}),p(n,q)}(a(ct,2),{label:"Submit",onClick:V}),p(l,u)},st=l=>{var u=Ht();p(l,u)};J(G,l=>{t(R)?l(at):l(st,!1)}),p(N,z),ht()}var Qt=h("<main><!></main>");function jt(N,C){dt(C,!1);let i=m();function x(e){const d=e.detail;B.postMessage({type:H.preferenceResultMessage,data:d})}function v(e){B.postMessage({type:H.preferenceNotify,data:e.detail})}B.postMessage({type:H.preferencePanelLoaded}),mt(),$("message",At,function(e){const d=e.data;d.type===H.preferenceInit&&c(i,d.data)}),Wt.Root(N,{children:(e,d)=>{var _=Qt(),M=o(_),g=f=>{var r=qt(),A=bt(r),y=s=>{Nt(s,{get inputData(){return t(i)},$$events:{result:x,notify:v}})};J(A,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};J(M,f=>{t(i)&&f(g)}),p(e,_)},$$slots:{default:!0}}),ht()}(async function(){B&&B.initialize&&await B.initialize(),Bt(jt,{target:document.getElementById("app")})})();
