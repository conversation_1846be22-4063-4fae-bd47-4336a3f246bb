var Vi=Object.defineProperty;var Ti=(pe,n,S)=>n in pe?Vi(pe,n,{enumerable:!0,configurable:!0,writable:!0,value:S}):pe[n]=S;var ni=(pe,n,S)=>Ti(pe,typeof n!="symbol"?n+"":n,S);import{A as it,C as E,X as N,Y as t,m as c,K as a,_ as e,$ as Mt,D as st,J as v,H as R,t as l,am as ri,Q as ye,a3 as se,ab as jt,S as Xt,b as i,I as nt,V as de,W as Ze,L as o,ak as ei,a6 as $i,M as q,O as Ci,P as ti,R as Qe,B as Ri,a4 as xt,w as di,a2 as Wt,a$ as bi,aE as zt,G as we,F as Ft,v as hi,u as Bt,ax as ji,T as qe,f as Li,ay as oi,al as Hi,az as Ii}from"./SpinnerAugment-AffdR7--.js";import"./design-system-init-BpU1o6o4.js";/* empty css                                */import{W as ki,a as Bi,I as yi,e as dt,i as mt,c as qi}from"./IconButtonAugment-DVt24OaC.js";import{M as Ui}from"./message-broker-Bv_1VsFe.js";import{R as Ut}from"./ra-diff-ops-model-jvQuAtrB.js";import{C as Wi,a as Gi,T as Ai,b as Ji,k as Yi}from"./CollapseButtonAugment-DgGSnbBS.js";import{t as Ki,s as Qi}from"./index-oHUUsc-1.js";import{c as ui,p as Xi,M as es,g as Rt,a as ts,i as is,b as ss,P as ci,O as Ei,D as ns,C as as,E as ls}from"./diff-operations-zlr5gNTh.js";import{a as os,b as rs,g as ds,M as cs}from"./index-ZbZe59K6.js";import{V as gi}from"./VSCodeCodicon-CM9n-Tfg.js";import{d as vs,T as St,a as Ot,C as fs}from"./CardAugment-CB88N7dm.js";import{B as _t}from"./ButtonAugment-K-zrKZyw.js";import{M as mi}from"./MaterialIcon-DgNf9Yde.js";import{i as ps,b as Qt,c as hs,d as zi,n as us,g as Te,a as ai,M as gs}from"./focusTrapStack-CB_5BS9R.js";import{F as ms,g as li,p as Fi,d as _s}from"./index-C4gKbsWy.js";import{L as Di}from"./LanguageIcon-CVD96Tjf.js";import{A as ws}from"./async-messaging-DXXiLgz5.js";import{E as xi}from"./exclamation-triangle-DW_Brj7M.js";import{F as ys}from"./Filespan-CLvGlhI3.js";import{M as $s}from"./ModalAugment-C7SE8sNA.js";import"./svelte-component-BzMfvILK.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C5DcjNTh.js";class vi{constructor(n){ni(this,"_opts",null);ni(this,"_subscribers",new Set);this._asyncMsgSender=n}subscribe(n){return this._subscribers.add(n),n(this),()=>{this._subscribers.delete(n)}}notifySubscribers(){this._subscribers.forEach(n=>n(this))}get opts(){return this._opts}updateOpts(n){this._opts=n,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const n=await this._asyncMsgSender.send({type:ki.remoteAgentDiffPanelLoaded});this.updateOpts(n.data)}catch(n){console.error("Failed to load diff panel:",n),this.updateOpts(null)}}handleMessageFromExtension(n){const S=n.data;return!(!S||!S.type)&&S.type===ki.remoteAgentDiffPanelSetOpts&&(this.updateOpts(S.data),!0)}}ni(vi,"key","remoteAgentDiffModel");var Cs=v("<span><code><!></code></span>");function bs(pe,n){it(n,!1);const S=c(),$e=c(),h=c(),f=c();let O=E(n,"token",8),le=E(n,"element",28,()=>{});N(()=>a(O()),()=>{t(S,O().raw.slice(1,O().raw.length-1))}),N(()=>e(S),()=>{t($e,e(S).startsWith('"'))}),N(()=>e(S),()=>{t(h,/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(e(S)))}),N(()=>(e(h),e(S)),()=>{t(f,e(h)&&function(y){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(y))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let Z,I,ne;return y.length===4?(Z=parseInt(y.charAt(1),16),I=parseInt(y.charAt(2),16),ne=parseInt(y.charAt(3),16),Z*=17,I*=17,ne*=17):(Z=parseInt(y.slice(1,3),16),I=parseInt(y.slice(3,5),16),ne=parseInt(y.slice(5,7),16)),.299*Z+.587*I+.114*ne<130}(e(S)))}),Mt(),st();var j=Cs(),ce=l(j);let M;var K=l(ce),D=y=>{var Z=de();ye(()=>Ze(Z,e(S))),i(y,Z)},A=y=>{var Z=de();ye(()=>Ze(Z,e(S))),i(y,Z)};R(K,y=>{e(h)?y(D):y(A,!1)}),ri(j,y=>le(y),()=>le()),ye(y=>{M=jt(ce,1,"markdown-codespan svelte-11ta4gi",null,M,y),Xt(ce,e(h)?`background-color: ${e(S)}; color: ${e(f)?"white":"black"}`:"")},[()=>({"markdown-string":e($e)})],se),i(pe,j),nt()}function _i(pe,n){let S=E(n,"markdown",8);const $e={codespan:bs},h=se(()=>(a(S()),o(()=>S().replace(/`?#[0-9a-fA-F]{3,6}`?/g,f=>f.startsWith("`")?f:`\`${f}\``))));es(pe,{get markdown(){return e(h)},get renderers(){return $e}})}function Mi(pe,n){return`${pe}:${n}`}const pi=(pe,n)=>{let S=null,$e=null,h=null,f=!1;function O(){h&&cancelAnimationFrame(h),h=requestAnimationFrame(()=>{const{path:M,onCollapseStateChange:K}=n;if(f)return void(h=null);const D=Array.from(document.querySelectorAll(`[data-description-id^="${M}:"]`));let A=!1;for(const y of D)if(y!==pe&&le(pe,y)){A=!0;break}A&&(f=!0),K&&K(A),h=null})}function le(M,K){const D=M.getBoundingClientRect(),A=K.getBoundingClientRect();return!(D.bottom<=A.top||A.bottom<=D.top)}function j(){ce(),S=new MutationObserver(()=>{O()});const M=pe.closest(".descriptions")||document.body;S.observe(M,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&($e=new ResizeObserver(()=>{O()}),$e.observe(pe)),window.addEventListener("resize",O),window.addEventListener("scroll",O)}function ce(){S&&(S.disconnect(),S=null),$e&&($e.disconnect(),$e=null),h&&(cancelAnimationFrame(h),h=null),window.removeEventListener("resize",O),window.removeEventListener("scroll",O)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{j(),O()}):requestAnimationFrame(()=>{j(),O()}),{update:M=>{n=M,f=!1,O()},destroy:ce}};var ks=v('<div role="region" aria-label="Code diff description"><div class="c-diff-description__content svelte-wweiw1"><!></div> <div class="c-diff-description__truncated-content svelte-wweiw1"><!> <div class="c-diff-description__expand-hint svelte-wweiw1">hover to expand</div></div></div>'),As=v('<div class="toggle-button svelte-1r29xbx"><!></div> <div class="descriptions svelte-1r29xbx"></div>',1),zs=v('<div><div class="editor-container svelte-1r29xbx"></div> <!></div>');function Fs(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(Q,"$monaco",S),f=()=>xt(_e,"$descriptionPositions",S),O=Ri();let le=E(n,"originalCode",8,""),j=E(n,"modifiedCode",8,""),ce=E(n,"path",8),M=E(n,"descriptions",24,()=>[]),K=E(n,"lineOffset",8,0),D=E(n,"extraPrefixLines",24,()=>[]),A=E(n,"extraSuffixLines",24,()=>[]),y=E(n,"theme",8),Z=E(n,"areDescriptionsVisible",12,!0),I=E(n,"isNewFile",8,!1),ne=E(n,"isDeletedFile",8,!1);const Q=os.getContext().monaco;let G,u=c(),z=c(),L=c(),m=[],ue=c();const F=rs();let b,r=di(0),B=c(I()?20*j().split(`
`).length+40:100);const me=h()?h().languages.getLanguages().map($=>$.id):[];function Ee($,P){var J,ie,ae;if(P){const s=(J=P.split(".").pop())==null?void 0:J.toLowerCase();if(s){const k=(ae=(ie=h())==null?void 0:ie.languages.getLanguages().find(C=>{var _;return(_=C.extensions)==null?void 0:_.includes("."+s)}))==null?void 0:ae.id;if(k&&me.includes(k))return k}}return"plaintext"}const _e=di({});let Ae=null;function Me(){if(!e(u))return;m=m.filter(J=>(J.dispose(),!1));const $=e(u).getOriginalEditor(),P=e(u).getModifiedEditor();m.push($.onDidScrollChange(()=>{bi(r,$.getScrollTop())}),P.onDidScrollChange(()=>{bi(r,P.getScrollTop())}))}function oe(){if(!e(u)||!e(ue))return;const $=e(u).getOriginalEditor(),P=e(u).getModifiedEditor();m.push(P.onDidContentSizeChange(()=>F.requestLayout()),$.onDidContentSizeChange(()=>F.requestLayout()),e(u).onDidUpdateDiff(()=>F.requestLayout()),P.onDidChangeHiddenAreas(()=>F.requestLayout()),$.onDidChangeHiddenAreas(()=>F.requestLayout()),P.onDidLayoutChange(()=>F.requestLayout()),$.onDidLayoutChange(()=>F.requestLayout()),P.onDidFocusEditorWidget(()=>{Ye(!0)}),$.onDidFocusEditorWidget(()=>{Ye(!0)}),P.onDidBlurEditorWidget(()=>{Ye(!1)}),$.onDidBlurEditorWidget(()=>{Ye(!1)}),P.onDidChangeModelContent(()=>{var s;Ve=!0,De=Date.now();const J=((s=e(L))==null?void 0:s.getValue())||"";if(J===j())return;const ie=J.replace(D().join(""),"").replace(A().join(""),"");O("codeChange",{modifiedCode:ie});const ae=setTimeout(()=>{Ve=!1},500);m.push({dispose:()=>clearTimeout(ae)})})),function(){!e(ue)||!e(u)||(Ae&&clearTimeout(Ae),Ae=setTimeout(()=>{if(!e(ue).__hasClickListener){const J=ie=>{const ae=ie.target;ae&&(ae.closest('[title="Show Unchanged Region"]')||ae.closest('[title="Hide Unchanged Region"]'))&&Re()};e(ue).addEventListener("click",J),zt(ue,e(ue).__hasClickListener=!0),m.push({dispose:()=>{e(ue).removeEventListener("click",J)}})}e(u)&&m.push(e(u).onDidUpdateDiff(()=>{Re()}))},300))}()}$i(()=>{var $,P,J;($=e(u))==null||$.dispose(),(P=e(z))==null||P.dispose(),G==null||G.dispose(),(J=e(L))==null||J.dispose(),m.forEach(ie=>ie.dispose()),Ae&&clearTimeout(Ae),b==null||b()});let ge=null;function Re(){ge&&clearTimeout(ge),ge=setTimeout(()=>{F.requestLayout(),ge=null},100),ge&&m.push({dispose:()=>{ge&&(clearTimeout(ge),ge=null)}})}function Be($,P,J,ie=[],ae=[]){var C;if(!h())return void console.error("Monaco not loaded. Diff view cannot be updated.");G==null||G.dispose(),(C=e(L))==null||C.dispose(),P=P||"",J=J||"";const s=ie.join(""),k=ae.join("");if(P=I()?J.split(`
`).map(()=>" ").join(`
`):s+P+k,J=s+J+k,G=h().editor.createModel(P,void 0,$!==void 0?h().Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0),ne()&&(J=J.split(`
`).map(()=>" ").join(`
`)),t(L,h().editor.createModel(J,void 0,$!==void 0?h().Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0)),e(u)){e(u).setModel({original:G,modified:e(L)});const _=e(u).getOriginalEditor();_&&_.updateOptions({lineNumbers:"off"}),Me(),Ae&&clearTimeout(Ae),Ae=setTimeout(()=>{oe(),Ae=null},300)}}ei(()=>{if(h())if(I()){t(z,h().editor.create(e(ue),{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:y(),scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:ie=>`${K()-D().length+ie}`}));const $=Ee(j(),ce());t(L,h().editor.createModel(j(),$,ce()!==void 0?h().Uri.parse("file://"+ce()+`#${crypto.randomUUID()}`):void 0)),e(z).setModel(e(L)),m.push(e(z).onDidChangeModelContent(()=>{var s;Ve=!0,De=Date.now();const ie=((s=e(L))==null?void 0:s.getValue())||"";if(ie===j())return;O("codeChange",{modifiedCode:ie});const ae=setTimeout(()=>{Ve=!1},500);m.push({dispose:()=>clearTimeout(ae)})})),m.push(e(z).onDidFocusEditorWidget(()=>{var ie;(ie=e(z))==null||ie.updateOptions({scrollbar:{handleMouseWheel:!0}})}),e(z).onDidBlurEditorWidget(()=>{var ie;(ie=e(z))==null||ie.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const P=e(z).getContentHeight();t(B,Math.max(P,60));const J=setTimeout(()=>{var ie;(ie=e(z))==null||ie.layout()},0);m.push({dispose:()=>clearTimeout(J)})}else t(u,h().editor.createDiffEditor(e(ue),{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:y(),scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:$=>`${K()-D().length+$}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),b&&b(),b=F.registerEditor({editor:e(u),updateHeight:Je,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),Be(ce(),le(),j(),D(),A()),Me(),oe(),Ae&&clearTimeout(Ae),Ae=setTimeout(()=>{F.requestLayout(),Ae=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ve=!1,De=0;function ze($,P=!0){return e(u)?(P?e(u).getModifiedEditor():e(u).getOriginalEditor()).getTopForLineNumber($):18*$}function Je(){if(!e(u))return;const $=e(u).getModel(),P=$==null?void 0:$.original,J=$==null?void 0:$.modified;if(!P||!J)return;const ie=e(u).getOriginalEditor(),ae=e(u).getModifiedEditor(),s=e(u).getLineChanges()||[];let k;if(s.length===0){const C=ie.getContentHeight(),_=ae.getContentHeight();k=Math.max(100,C,_)}else{let C=0,_=0;for(const x of s)x.originalEndLineNumber>0&&(C=Math.max(C,x.originalEndLineNumber)),x.modifiedEndLineNumber>0&&(_=Math.max(_,x.modifiedEndLineNumber));C=Math.min(C+3,P.getLineCount()),_=Math.min(_+3,J.getLineCount());const T=ie.getTopForLineNumber(C),g=ae.getTopForLineNumber(_);k=Math.max(T,g)+60}t(B,Math.min(k,2e4)),e(u).layout(),Ue()}function Ye($){if(!e(u))return;const P=e(u).getOriginalEditor(),J=e(u).getModifiedEditor();P.updateOptions({scrollbar:{handleMouseWheel:$}}),J.updateOptions({scrollbar:{handleMouseWheel:$}})}function je($){if(!e(u))return e(z)?e(z).getTopForLineNumber($.range.start+1):0;const P=e(u).getModel(),J=P==null?void 0:P.original,ie=P==null?void 0:P.modified;if(!J||!ie)return 0;const ae=ze($.range.start+1,!1),s=ze($.range.start+1,!0);return ae&&!s?ae:!ae&&s?s:Math.min(ae,s)}function Ue(){if(!e(u)&&!e(z)||M().length===0)return;const $={};M().forEach((P,J)=>{$[J]=je(P)}),function(P,J=50){const ie=Object.keys(P).sort((ae,s)=>P[Number(ae)]-P[Number(s)]);for(let ae=0;ae<ie.length-1;ae++){const s=Number(ie[ae]),k=P[s];P[s+1]-k<J&&(P[Number(ie[ae+1])]=k+J)}}($),_e.set($)}const at=crypto.randomUUID();N(()=>(a(j()),a(I()),e(z),e(L),a(ce()),h(),e(u),a(le()),a(D()),a(A())),()=>{if($=j(),!(Ve||Date.now()-De<1e3||e(L)&&e(L).getValue()===D().join("")+$+A().join("")))if(I()&&e(z)){if(e(L))e(L).setValue(j());else{const P=Ee(j(),ce());h()&&t(L,h().editor.createModel(j(),P,ce()!==void 0?h().Uri.parse("file://"+ce()+`#${crypto.randomUUID()}`):void 0)),e(L)&&e(z).setModel(e(L))}t(B,20*j().split(`
`).length+40),e(z).layout()}else!I()&&e(u)&&(Be(ce(),le(),j(),D(),A()),F.requestLayout());var $}),N(()=>(e(u),e(z),a(M())),()=>{(e(u)||e(z))&&M().length>0&&Ue()}),N(()=>(a(I()),a(j()),e(z)),()=>{if(I()&&j()&&e(z)){const $=e(z).getContentHeight();t(B,Math.max($,60)),e(z).layout()}}),Mt(),st();var Xe=zs();let ct;var Oe=l(Xe);ri(Oe,$=>t(ue,$),()=>e(ue));var be=q(Oe,2),We=$=>{var P=As(),J=we(P),ie=l(J);yi(ie,{variant:"ghost",color:"neutral",size:1,$$events:{click:()=>Z(!Z())},children:(s,k)=>{var C=Ft(),_=we(C),T=x=>{gi(x,{icon:"x"})},g=x=>{gi(x,{icon:"book"})};R(_,x=>{Z()?x(T):x(g,!1)}),i(s,C)},$$slots:{default:!0}});var ae=q(J,2);dt(ae,5,M,mt,(s,k,C)=>{const _=se(()=>(f(),e(k),o(()=>f()[C]||je(e(k)))));(function(T,g){it(g,!1);const x=c();let d=E(g,"description",8),V=E(g,"position",8),p=E(g,"fileId",8),W=E(g,"index",8),Y=c(!1),ee=c(!1),w=c(),X=c(0);const H=vs(Ce=>{t(Y,Ce)},100);function re(Ce){const Pe=document.createElement("canvas").getContext("2d");return Pe?Pe.measureText(Ce).width:8*Ce.length}function te(){if(e(w)){const Ce=e(w).getBoundingClientRect();t(X,Ce.width-128)}}let ve=null;ei(()=>{e(w)&&typeof ResizeObserver<"u"&&(ve=new ResizeObserver(()=>{te()}),ve.observe(e(w)),te())}),$i(()=>{ve&&ve.disconnect()}),N(()=>e(w),()=>{e(w)&&te()}),N(()=>(a(d()),e(X)),()=>{t(x,(()=>{const Ce=d().text.split(`
`)[0].split(" ");let Pe="";if(e(X)<=0)for(const vt of Ce){const lt=Pe+(Pe?" ":"")+vt;if(lt.length>30)break;Pe=lt}else for(const vt of Ce){const lt=Pe+(Pe?" ":"")+vt;if(re(lt+"...")>e(X))break;Pe=lt}return Pe+"..."})())}),Mt(),st();var fe=ks();let U;var ke=l(fe);_i(l(ke),{get markdown(){return a(d()),o(()=>d().text)}});var He=q(ke,2);_i(l(He),{get markdown(){return e(x)}}),ri(fe,Ce=>t(w,Ce),()=>e(w)),Bi(fe,(Ce,Pe)=>pi==null?void 0:pi(Ce,Pe),()=>({path:p(),onCollapseStateChange:Ce=>t(ee,Ce)})),Ci(()=>ti("mouseenter",fe,Ce=>{H.cancel(),t(Y,!0),Ce.stopPropagation()})),Ci(()=>ti("mouseleave",fe,Ce=>{H(!1),Ce.stopPropagation()})),ye((Ce,Pe)=>{U=jt(fe,1,"c-diff-description svelte-wweiw1",null,U,Ce),Xt(fe,`top: ${V()??""}px;`),Qe(fe,"data-description-id",Pe)},[()=>({"c-diff-description__collapsed":e(ee)&&!e(Y),"c-diff-description__hovered":e(Y)}),()=>(a(Mi),a(p()),a(W()),o(()=>Mi(p(),W())))],se),i(T,fe),nt()})(s,{get description(){return e(k)},get position(){return e(_)},get fileId(){return at},index:C})}),ye(()=>Xt(ae,`transform: translateY(${-xt(r,"$scrollY",S)}px)`)),i($,P)};R(be,$=>{a(M()),o(()=>M().length>0)&&$(We)}),ye($=>{ct=jt(Xe,1,"monaco-diff-container svelte-1r29xbx",null,ct,$),Xt(Oe,`height: ${e(B)??""}px`)},[()=>({"monaco-diff-container-with-descriptions":M().length>0&&Z()})],se),i(pe,Xe),nt(),$e()}const Pi=Symbol("focusedPath");function Ni(){return Bt(Pi)}function wi(pe){return`file-diff-${Rt(pe)}`}var xs=v('<!> <img class="image-preview svelte-1536g7w"/>',1),Ms=v("<!> <!>",1),Ls=v("<!> ",1),qs=v('<!> <img class="image-preview image-preview--previous svelte-1536g7w"/>',1),Es=v('<!> <img class="image-preview svelte-1536g7w"/> <!>',1),Ds=v('<div class="image-container svelte-1536g7w"><!></div>'),Ps=v("<!> No text preview available.",1),Ns=v('<div class="binary-file-message svelte-1536g7w"><!></div>'),Os=v('<div class="too-large-message svelte-1536g7w"><!></div>'),Ss=v('<div class="changes svelte-1536g7w"><!></div>'),Zs=v('<span class="c-directory svelte-1536g7w"> </span>'),Vs=v('<span class="new-file-badge svelte-1536g7w">New File</span>'),Ts=v('<span class="additions svelte-1536g7w"><!></span>'),Rs=v('<span class="deletions svelte-1536g7w"><!></span>'),js=v('<div class="changes-indicator svelte-1536g7w"><!> <!></div>'),Hs=v('<div class="applied svelte-1536g7w"><!></div>'),Is=v('<div class="applied__icon svelte-1536g7w"><!></div>'),Bs=v("<!> <!>",1),Us=v('<div slot="header" class="header svelte-1536g7w"><!> <div class="c-path svelte-1536g7w"><!> <!></div> <!> <!> <!></div>'),Ws=v("<div><!></div>");function Oi(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(ji,"$themeStore",S),f=c(),O=c(),le=c(),j=c(),ce=c(),M=c(),K=c(),D=c(),A=c(),y=c(),Z=c(),I=c(),ne=c(),Q=c(),G=c(),u=c(),z=c(),L=c(),m=c(),ue=c(),F=c();let b=E(n,"path",8),r=E(n,"change",12),B=E(n,"descriptions",24,()=>[]),me=E(n,"areDescriptionsVisible",12,!0),Ee=E(n,"isExpandedDefault",8),_e=E(n,"isCollapsed",28,()=>!Ee()),Ae=E(n,"isApplying",8),Me=E(n,"hasApplied",8),oe=E(n,"onApplyChanges",24,()=>{}),ge=E(n,"onCodeChange",24,()=>{}),Re=E(n,"onOpenFile",24,()=>{}),Be=E(n,"isAgentFromDifferentRepo",8,!1);const Ve=Ni(),De=Bt(Ut.key);let ze=c(r().modifiedCode);function Je(be){var We;t(ze,be.detail.modifiedCode),(We=ge())==null||We(e(ze))}function Ye(){var be,We;De.reportApplyChangesEvent(),r(r().modifiedCode=e(ze),!0),(be=ge())==null||be(e(ze)),(We=oe())==null||We()}let je=c(e(m));function Ue(){t(je,`Open ${e(m)??"file"}`)}async function at(){Re()&&(t(je,"Opening file..."),await Re()()?Ue():(t(je,"Failed to open file. Does the file exist?"),setTimeout(()=>{Ue()},2e3)))}ei(()=>{Ue()}),N(()=>a(r()),()=>{t(ze,r().modifiedCode)}),N(()=>a(r()),()=>{t(f,ts(r().diff))}),N(()=>e(f),()=>{t(O,e(f).additions)}),N(()=>e(f),()=>{t(le,e(f).deletions)}),N(()=>a(r()),()=>{t(j,is(r()))}),N(()=>a(r()),()=>{t(ce,ss(r()))}),N(()=>a(b()),()=>{t(M,ps(b()))}),N(()=>a(b()),()=>{t(K,Qt(b()))}),N(()=>a(b()),()=>{t(D,hs(b()))}),N(()=>a(r()),()=>{var be;t(A,((be=r().originalCode)==null?void 0:be.length)||0)}),N(()=>e(ze),()=>{var be;t(y,((be=e(ze))==null?void 0:be.length)||0)}),N(()=>e(A),()=>{t(Z,zi(e(A)))}),N(()=>e(y),()=>{t(I,zi(e(y)))}),N(()=>(e(ze),a(r())),()=>{t(ne,!e(ze)&&!!r().originalCode)}),N(()=>(e(ze),a(r())),()=>{t(Q,!!e(ze)&&!r().originalCode)}),N(()=>e(M),()=>{t(G,e(M))}),N(()=>(e(M),e(D)),()=>{t(u,!e(M)&&e(D))}),N(()=>(e(M),e(D),e(I),e(ne),e(Z),e(Q)),()=>{t(z,!e(M)&&!e(D)&&(e(I)||e(ne)&&e(Z)||e(Q)&&e(I)))}),N(()=>h(),()=>{var be,We;t(L,ds((be=h())==null?void 0:be.category,(We=h())==null?void 0:We.intensity))}),N(()=>a(b()),()=>{t(m,us(b()))}),N(()=>(a(Ae()),a(Be())),()=>{t(ue,Ae()||Be())}),N(()=>(a(Ae()),a(Me()),a(Be())),()=>{t(F,Ae()?"Applying changes...":Me()?"Reapply changes to local file":Be()?"Cannot apply changes from a different repository locally":"Apply changes to local file")}),Mt(),st();var Xe=Ws();let ct;var Oe=l(Xe);Wi(Oe,{stickyHeader:!0,get collapsed(){return _e()},set collapsed(be){_e(be)},children:(be,We)=>{var $=Ss(),P=l($),J=ae=>{var s=Ds(),k=l(s),C=T=>{var g=Ms(),x=we(g);qe(x,{class:"image-info-text",children:(p,W)=>{var Y=de();ye(ee=>Ze(Y,`Image deleted: ${ee??""}`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(p,Y)},$$slots:{default:!0}});var d=q(x,2),V=p=>{var W=xs(),Y=we(W);qe(Y,{class:"image-info-text",children:(w,X)=>{var H=de("Previous version:");i(w,H)},$$slots:{default:!0}});var ee=q(Y,2);ye((w,X,H)=>{Qe(ee,"src",`data:${w??""};base64,${X??""}`),Qe(ee,"alt",`Original ${H??""}`)},[()=>(a(Qt),a(b()),o(()=>Qt(b()))),()=>(a(r()),o(()=>btoa(r().originalCode))),()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(p,W)};R(d,p=>{a(r()),o(()=>r().originalCode)&&p(V)}),i(T,g)},_=(T,g)=>{var x=d=>{var V=Es(),p=we(V);qe(p,{class:"image-info-text",children:(w,X)=>{var H=Ls(),re=we(H),te=U=>{var ke=de("New image added");i(U,ke)},ve=U=>{var ke=de("Image modified");i(U,ke)};R(re,U=>{e(j)||e(Q)?U(te):U(ve,!1)});var fe=q(re);ye(U=>Ze(fe,`: ${U??""}`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(w,H)},$$slots:{default:!0}});var W=q(p,2),Y=q(W,2),ee=w=>{var X=qs(),H=we(X);qe(H,{class:"image-info-text",children:(te,ve)=>{var fe=de("Previous version:");i(te,fe)},$$slots:{default:!0}});var re=q(H,2);ye((te,ve,fe)=>{Qe(re,"src",`data:${te??""};base64,${ve??""}`),Qe(re,"alt",`Original ${fe??""}`)},[()=>(a(Qt),a(b()),o(()=>Qt(b()))),()=>(a(r()),o(()=>btoa(r().originalCode))),()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(w,X)};R(Y,w=>{a(r()),e(ze),e(j),o(()=>r().originalCode&&e(ze)!==r().originalCode&&!e(j))&&w(ee)}),ye((w,X)=>{Qe(W,"src",`data:${e(K)??""};base64,${w??""}`),Qe(W,"alt",`Current ${X??""}`)},[()=>(e(ze),o(()=>btoa(e(ze)))),()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(d,V)};R(T,d=>{e(ze)&&d(x)},g)};R(k,T=>{e(ne)?T(C):T(_,!1)}),i(ae,s)},ie=(ae,s)=>{var k=_=>{var T=Ns(),g=l(T);qe(g,{children:(x,d)=>{var V=Ps(),p=we(V),W=ee=>{var w=de();ye(X=>Ze(w,`Binary file added: ${X??""}.`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(ee,w)},Y=(ee,w)=>{var X=re=>{var te=de();ye(ve=>Ze(te,`Binary file deleted: ${ve??""}.`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(re,te)},H=re=>{var te=de();ye(ve=>Ze(te,`Binary file modified: ${ve??""}.`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(re,te)};R(ee,re=>{e(ne)?re(X):re(H,!1)},w)};R(p,ee=>{e(j)||e(Q)?ee(W):ee(Y,!1)}),i(x,V)},$$slots:{default:!0}}),i(_,T)},C=(_,T)=>{var g=d=>{var V=Os(),p=l(V);qe(p,{size:1,children:(W,Y)=>{var ee=de();ye(w=>Ze(ee,`File "${w??""}" is too large to display a diff (size: ${(e(ne)?e(A):e(y))??""} bytes, max: ${gs} bytes).`),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(W,ee)},$$slots:{default:!0}}),i(d,V)},x=d=>{Fs(d,{get path(){return b()},get originalCode(){return a(r()),o(()=>r().originalCode)},get modifiedCode(){return e(ze)},get theme(){return e(L)},get descriptions(){return B()},get isNewFile(){return e(j)},get isDeletedFile(){return e(ce)},get areDescriptionsVisible(){return me()},set areDescriptionsVisible(V){me(V)},$$events:{codeChange:Je},$$legacy:!0})};R(_,d=>{e(z)?d(g):d(x,!1)},T)};R(ae,_=>{e(u)?_(k):_(C,!1)},s)};R(P,ae=>{e(G)?ae(J):ae(ie,!1)}),i(be,$)},$$slots:{default:!0,header:(be,We)=>{var $=Us(),P=l($);Gi(P,{});var J=q(P,2),ie=l(J);const ae=se(()=>(a(Ot),o(()=>[Ot.Hover])));St(ie,{get content(){return e(je)},get triggerOn(){return e(ae)},delayDurationMs:300,children:(p,W)=>{_t(p,{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$events:{click:at},children:(Y,ee)=>{var w=de();ye(X=>Ze(w,X),[()=>(a(Te),e(m),o(()=>Te(e(m))))],se),i(Y,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var s=q(ie,2),k=p=>{var W=Zs(),Y=l(W);ye(ee=>Ze(Y,ee),[()=>(a(ai),e(m),o(()=>ai(e(m))))],se),i(p,W)};R(s,p=>{a(ai),e(m),o(()=>ai(e(m)))&&p(k)});var C=q(J,2),_=p=>{var W=Vs();i(p,W)},T=p=>{var W=js(),Y=l(W),ee=H=>{var re=Ts(),te=l(re);qe(te,{size:1,children:(ve,fe)=>{var U=de();ye(()=>Ze(U,`+${e(O)??""}`)),i(ve,U)},$$slots:{default:!0}}),i(H,re)};R(Y,H=>{e(O)>0&&H(ee)});var w=q(Y,2),X=H=>{var re=Rs(),te=l(re);qe(te,{size:1,children:(ve,fe)=>{var U=de();ye(()=>Ze(U,`-${e(le)??""}`)),i(ve,U)},$$slots:{default:!0}}),i(H,re)};R(w,H=>{e(le)>0&&H(X)}),i(p,W)};R(C,p=>{e(j)?p(_):p(T,!1)});var g=q(C,2);const x=se(()=>(a(Ot),o(()=>[Ot.Hover])));St(g,{get content(){return e(F)},get triggerOn(){return e(x)},delayDurationMs:300,children:(p,W)=>{_t(p,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(ue)},$$events:{click:Ye},children:(Y,ee)=>{var w=Bs(),X=we(w),H=U=>{var ke=de("Applied");i(U,ke)},re=U=>{var ke=de("Apply");i(U,ke)};R(X,U=>{Me()?U(H):U(re,!1)});var te=q(X,2),ve=U=>{var ke=Hs(),He=l(ke);mi(He,{iconName:"check"}),i(U,ke)},fe=U=>{var ke=Is(),He=l(ke);ci(He,{}),i(U,ke)};R(te,U=>{Me()?U(ve):U(fe,!1)}),i(Y,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var d=q(g,2),V=p=>{const W=se(()=>(a(Ot),o(()=>[Ot.Hover])));St(p,{get content(){return e(je)},get triggerOn(){return e(W)},delayDurationMs:300,children:(Y,ee)=>{yi(Y,{size:1,variant:"ghost",color:"neutral",$$events:{click:at},children:(w,X)=>{Ei(w,{})},$$slots:{default:!0}})},$$slots:{default:!0}})};R(d,p=>{Me()&&p(V)}),i(be,$)}},$$legacy:!0}),ye((be,We)=>{ct=jt(Xe,1,"c svelte-1536g7w",null,ct,be),Qe(Xe,"id",We)},[()=>({focused:xt(Ve,"$focusedPath",S)===b()}),()=>(a(wi),a(b()),o(()=>wi(b())))],se),i(pe,Xe),nt(),$e()}var Gs=v('<span class="full-path-text svelte-qnxoj"> </span>'),Js=v('<div class="tree-node__children svelte-qnxoj" role="group"></div>'),Ys=v('<div class="tree-node svelte-qnxoj"><div role="treeitem" tabindex="0"><div class="tree-node__indent svelte-qnxoj"></div> <div class="tree-node__icon-container svelte-qnxoj"><!></div> <span><!></span></div> <!></div>');function Si(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(le,"$focusedPath",S);let f=E(n,"node",12),O=E(n,"indentLevel",8,0);const le=Ni();function j(){f().isFile?le.set(f().path):f(f().isExpanded=!f().isExpanded,!0)}st();var ce=Ys(),M=l(ce);let K;var D=l(M),A=q(D,2),y=l(A),Z=L=>{const m=se(()=>(a(f()),o(()=>f().isExpanded?"chevron-down":"chevron-right")));gi(L,{get icon(){return e(m)}})},I=L=>{Di(L,{get filename(){return a(f()),o(()=>f().name)}})};R(y,L=>{a(f()),o(()=>!f().isFile)?L(Z):L(I,!1)});var ne=q(A,2);let Q;var G=l(ne);qe(G,{size:1,children:(L,m)=>{var ue=Gs(),F=l(ue);ye(()=>Ze(F,(a(f()),o(()=>f().displayName||f().name)))),i(L,ue)},$$slots:{default:!0}});var u=q(M,2),z=L=>{var m=Js();dt(m,5,()=>(a(f()),o(()=>Array.from(f().children.values()).sort((ue,F)=>ue.isFile===F.isFile?ue.name.localeCompare(F.name):ue.isFile?1:-1))),mt,(ue,F)=>{var b=Ft(),r=we(b);const B=se(()=>O()+1);Si(r,{get node(){return e(F)},get indentLevel(){return e(B)}}),i(ue,b)}),i(L,m)};R(u,L=>{a(f()),o(()=>!f().isFile&&f().isExpanded&&f().children.size>0)&&L(z)}),ye((L,m)=>{K=jt(M,1,"tree-node__content svelte-qnxoj",null,K,L),Qe(M,"aria-selected",(a(f()),h(),o(()=>f().path===h()))),Qe(M,"aria-expanded",(a(f()),o(()=>f().isFile?void 0:f().isExpanded))),Xt(D,`width: ${6*O()}px`),Q=jt(ne,1,"tree-node__label svelte-qnxoj",null,Q,m),Qe(ne,"title",(a(f()),o(()=>f().displayName||f().name)))},[()=>({selected:f().path===h(),"collapsed-folder":f().displayName&&!f().isFile}),()=>({"full-path":f().displayName})],se),ti("click",M,j),ti("keydown",M,L=>L.key==="Enter"&&j()),i(pe,ce),nt(),$e()}var Ks=v('<div class="tree-view__loading svelte-1tnd9l7"><div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div></div>'),Qs=v('<div class="tree-view__empty svelte-1tnd9l7"><!></div>'),Xs=v('<div class="tree-view svelte-1tnd9l7"><div class="tree-view__content svelte-1tnd9l7" role="tree" aria-label="Changed Files"><!></div></div>');function Zi(pe,n){it(n,!1);const S=c();let $e=E(n,"changedFiles",24,()=>[]),h=E(n,"isLoading",8,!1);function f(D){const A={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return D.forEach(y=>{const Z=y.change_type===ms.deleted?y.old_path:y.new_path;Z&&function(I,ne){const Q=ne.split("/");let G=I;for(let u=0;u<Q.length;u++){const z=Q[u],L=u===Q.length-1,m=Q.slice(0,u+1).join("/");G.children.has(z)||G.children.set(z,{name:z,path:m,isFile:L,children:new Map,isExpanded:!0}),G=G.children.get(z)}}(A,Z)}),function(y){if(!y.isFile){if(y.path===""){const Z=Array.from(y.children.values()).filter(I=>!I.isFile);for(const I of Z)O(I,!0);return}O(y)}}(A),A}function O(D,A=!1){if(D.isFile)return;let y="";A&&(y=function(Q){let G=Q.path.split("/"),u=Q;for(;;){const z=Array.from(u.children.values()).filter(m=>!m.isFile),L=Array.from(u.children.values()).filter(m=>m.isFile);if(z.length!==1||L.length!==0)break;u=z[0],G.push(u.name)}return G.join("/")}(D));const Z=Array.from(D.children.values()).filter(Q=>!Q.isFile);for(const Q of Z)O(Q);const I=Array.from(D.children.values()).filter(Q=>!Q.isFile),ne=Array.from(D.children.values()).filter(Q=>Q.isFile);if(I.length===1&&ne.length===0){const Q=I[0],G=Q.name;if(A){D.displayName=y||`${D.name}/${G}`;for(const[u,z]of Q.children.entries()){const L=`${u}`;D.children.set(L,z)}D.children.delete(G)}else{D.displayName?Q.displayName=`${D.displayName}/${G}`:Q.displayName=`${D.name}/${G}`;for(const[u,z]of Q.children.entries()){const L=`${G}/${u}`;D.children.set(L,z)}D.children.delete(G)}}}N(()=>a($e()),()=>{t(S,f($e()))}),Mt(),st();var le=Xs(),j=l(le),ce=l(j),M=D=>{var A=Ks();i(D,A)},K=(D,A)=>{var y=I=>{var ne=Qs(),Q=l(ne);qe(Q,{size:1,color:"neutral",children:(G,u)=>{var z=de("No changed files");i(G,z)},$$slots:{default:!0}}),i(I,ne)},Z=I=>{var ne=Ft(),Q=we(ne);dt(Q,1,()=>(e(S),o(()=>Array.from(e(S).children.values()).sort((G,u)=>G.isFile===u.isFile?G.name.localeCompare(u.name):G.isFile?1:-1))),mt,(G,u)=>{Si(G,{get node(){return e(u)},indentLevel:0})}),i(I,ne)};R(D,I=>{e(S),o(()=>e(S).children.size===0)?I(y):I(Z,!1)},A)};R(ce,D=>{h()?D(M):D(K,!1)}),i(pe,le),nt()}var en=v('<!> <div class="c-edits-list-controls__icon svelte-6iqvaj"><!></div>',1),tn=v("<div><!></div>"),sn=v('<div class="c-edits-list-header svelte-6iqvaj"><div class="c-edits-list-controls svelte-6iqvaj"><!></div></div> <div class="c-edits-list svelte-6iqvaj"><div class="c-edits-section svelte-6iqvaj"></div></div>',1),nn=v('<div class="c-edits-list c-edits-list--empty svelte-6iqvaj"><!></div>'),an=v('<div class="c-edits-list-container svelte-6iqvaj"><div class="c-file-explorer__layout svelte-6iqvaj"><div class="c-file-explorer__tree svelte-6iqvaj"><div class="c-file-explorer__tree__header svelte-6iqvaj"><!> <!></div></div> <div class="c-file-explorer__details svelte-6iqvaj"><!></div></div></div>');function ln(pe,n){it(n,!1);const S=c(),$e=c(),h=c(),f=c(),O=c();let le=E(n,"changedFiles",8),j=E(n,"onApplyChanges",8),ce=E(n,"onOpenFile",24,()=>{}),M=E(n,"pendingFiles",24,()=>[]),K=E(n,"appliedFiles",24,()=>[]),D=E(n,"isLoadingTreeView",8,!1),A=c({}),y=c(!1),Z=c(!1);function I(){if(!j())return;const b=e(O).map(B=>B.qualifiedPathName.relPath);if(b.every(B=>K().includes(B)))return void t(Z,!0);const r=b.filter(B=>!K().includes(B)&&!M().includes(B));r.length!==0&&(t(y,!0),t(Z,!1),r.forEach(B=>{const me=e(O).find(Ee=>Ee.qualifiedPathName.relPath===B);if(me){const Ee=e(A)[B]||me.newContents;j()(B,me.oldContents,Ee)}}))}N(()=>a(le()),()=>{t(S,JSON.stringify(le()))}),N(()=>a(K()),()=>{t($e,JSON.stringify(K()))}),N(()=>a(M()),()=>{t(h,JSON.stringify(M()))}),N(()=>e(S),()=>{e(S)&&(t(A,{}),t(y,!1),t(Z,!1))}),N(()=>(a(le()),e(A)),()=>{t(O,le().map(b=>{const r=b.new_path||b.old_path,B=b.old_contents||"",me=b.new_contents||"",Ee=ns.generateDiff(b.old_path,b.new_path,B,me),_e=function(Ae,Me){const oe=ui("oldFile","newFile",Ae,Me,"","",{context:3}),ge=Xi(oe);let Re=0,Be=0,Ve=[];for(const De of ge)for(const ze of De.hunks)for(const Je of ze.lines){const Ye=Je.startsWith("+"),je=Je.startsWith("-");Ye&&Re++,je&&Be++,Ve.push({value:Je,added:Ye,removed:je})}return{totalAddedLines:Re,totalRemovedLines:Be,changes:Ve,diff:oe}}(B,me);return e(A)[r]||zt(A,e(A)[r]=me),{qualifiedPathName:{rootPath:"",relPath:r},lineChanges:_e,oldContents:B,newContents:me,diff:Ee}}))}),N(()=>(e(S),e($e),e(h),e(O),a(K()),a(M())),()=>{t(f,(()=>{if(e(S)&&e($e)&&e(h)){const b=e(O).map(r=>r.qualifiedPathName.relPath);return b.length!==0&&b.some(r=>!K().includes(r)&&!M().includes(r))}return!1})())}),N(()=>(e(y),e(O),a(K()),a(M())),()=>{if(e(y)){const b=e(O).map(r=>r.qualifiedPathName.relPath);b.filter(r=>!K().includes(r)&&!M().includes(r)).length===0&&b.every(r=>K().includes(r)||M().includes(r))&&M().length===0&&K().length>0&&(t(y,!1),t(Z,!0))}}),N(()=>(e(O),e(y),e($e),a(K()),e(Z)),()=>{if(e(O).length>0&&!e(y)&&e($e)){const b=e(O).map(r=>r.qualifiedPathName.relPath);if(b.length>0){const r=b.every(B=>K().includes(B));r&&K().length>0?t(Z,!0):!r&&e(Z)&&t(Z,!1)}}}),Mt(),st();var ne=an(),Q=l(ne),G=l(Q),u=l(G),z=l(u);qe(z,{size:1,class:"c-file-explorer__tree__header__label",children:(b,r)=>{var B=de("Changed files");i(b,B)},$$slots:{default:!0}}),Zi(q(z,2),{get changedFiles(){return le()},get isLoading(){return D()}});var L=q(G,2),m=l(L),ue=b=>{var r=sn(),B=we(r),me=l(B),Ee=l(me),_e=oe=>{const ge=se(()=>(e(y),e(Z),a(M()),e(f),o(()=>e(y)||e(Z)||M().length>0||!e(f))));_t(oe,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(ge)},$$events:{click:I},children:(Re,Be)=>{var Ve=en(),De=we(Ve),ze=Ue=>{var at=de("Applying...");i(Ue,at)},Je=(Ue,at)=>{var Xe=Oe=>{var be=de("All applied");i(Oe,be)},ct=Oe=>{var be=de("Apply all");i(Oe,be)};R(Ue,Oe=>{e(Z)?Oe(Xe):Oe(ct,!1)},at)};R(De,Ue=>{e(y)?Ue(ze):Ue(Je,!1)});var Ye=q(De,2),je=l(Ye);ci(je,{}),i(Re,Ve)},$$slots:{default:!0}})};R(Ee,oe=>{e(O),o(()=>e(O).length>0)&&oe(_e)});var Ae=q(B,2),Me=l(Ae);dt(Me,5,()=>e(O),oe=>oe.qualifiedPathName.relPath,(oe,ge)=>{var Re=tn(),Be=l(Re);const Ve=se(()=>(a(M()),e(ge),o(()=>M().includes(e(ge).qualifiedPathName.relPath)))),De=se(()=>(a(K()),e(ge),o(()=>K().includes(e(ge).qualifiedPathName.relPath)))),ze=se(()=>ce()?()=>ce()(e(ge).qualifiedPathName.relPath):void 0);Oi(Be,{get path(){return e(ge),o(()=>e(ge).qualifiedPathName.relPath)},get change(){return e(ge),o(()=>e(ge).diff)},get isApplying(){return e(Ve)},get hasApplied(){return e(De)},onCodeChange:Je=>{(function(Ye,je){zt(A,e(A)[Ye]=je)})(e(ge).qualifiedPathName.relPath,Je)},onApplyChanges:()=>{const Je=e(A)[e(ge).qualifiedPathName.relPath]||e(ge).newContents;j()(e(ge).qualifiedPathName.relPath,e(ge).oldContents,Je)},get onOpenFile(){return e(ze)},isExpandedDefault:!0}),Ki(3,Re,()=>Qi),i(oe,Re)}),i(b,r)},F=b=>{var r=nn(),B=l(r);qe(B,{size:1,color:"neutral",children:(me,Ee)=>{var _e=de("No changes to show");i(me,_e)},$$slots:{default:!0}}),i(b,r)};R(m,b=>{e(O),o(()=>e(O).length>0)?b(ue):b(F,!1)}),i(pe,ne),nt()}var on=Li('<path fill-rule="evenodd" clip-rule="evenodd"></path>'),rn=Li('<svg width="14" viewBox="0 0 20 20" fill="currentColor" class="svelte-10h4f31"><!></svg>'),dn=v('<div class="c-skeleton-diff__controls svelte-1eiztmz"><div class="c-skeleton-diff__button svelte-1eiztmz"></div></div>'),cn=v('<div class="c-skeleton-diff__changes-item svelte-1eiztmz"><div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div></div>'),vn=v('<div class="c-skeleton-diff__subsection svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__changes svelte-1eiztmz"></div></div>'),fn=v('<div class="c-skeleton-diff__section svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div></div> <!></div> <!></div>'),pn=v('<div class="c-skeleton-diff svelte-1eiztmz"></div>'),hn=v("<!> <!>",1),un=v('<div class="c-conflicts-card__file svelte-1bce35u"><!> <!></div>'),gn=v('<div class="c-conflicts-card__header svelte-1bce35u"><div class="c-conflicts-card__icon svelte-1bce35u"><!></div> <span class="c-conflicts-card__title svelte-1bce35u"> </span></div> <div class="c-conflicts-card__description svelte-1bce35u"><!></div> <!>',1),mn=v('<div class="c-conflicts-card"><!></div>'),_n=v(`There are unstaged changes in your working directory. Please commit your changes or we will
      run <!> to stash your changes before applying changes from the remote agent.`,1),wn=v('<div class="c-unstaged-changes-modal__body svelte-9eyy34"><!> <!></div>'),yn=v('<div class="c-unstaged-changes-modal__stash-button-loading svelte-9eyy34"><!></div>'),$n=v("<!> <span>Stash & Apply Locally</span>",1),Cn=v('<div class="c-unstaged-changes-modal__footer svelte-9eyy34" slot="footer"><!> <!></div>'),bn=v('<div class="c-diff-view__error svelte-ibi4q5"><!> <!> <!></div>'),kn=v('<div class="c-diff-view__empty svelte-ibi4q5"><!></div>'),An=v("<!> <!>",1),zn=v("<!> <!>",1),Fn=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),xn=v("Applied <!>",1),Mn=v('<div class="c-diff-view__applied svelte-ibi4q5"><!></div>'),Ln=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),qn=v('<div class="c-diff-view__controls svelte-ibi4q5"><!></div> <!>',1),En=v('<div class="c-diff-view__skeleton-title svelte-ibi4q5"></div>'),Dn=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div> <div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>',1),Pn=v("<!> Collapse All",1),Nn=v("<!> Expand All",1),On=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),Sn=v('<div class="c-diff-view__applied svelte-ibi4q5"><!> <!></div>'),Zn=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Vn=v('<div class="c-diff-view__controls svelte-ibi4q5"><!> <!></div>'),Tn=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>'),Rn=v('<div class="c-diff-view__warning svelte-ibi4q5"><!> </div>'),jn=v('<div class="c-diff-view__changes-item svelte-ibi4q5"><!></div>'),Hn=v('<div class="c-diff-view__subsection svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><div class="c-diff-view__icon svelte-ibi4q5"><!></div> <h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <!></div></div> <div class="c-diff-view__changes svelte-ibi4q5"></div></div>'),In=v('<div class="c-diff-view__section svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <div class="c-diff-view__description svelte-ibi4q5"><!></div></div> <!></div> <!> <!></div>'),Bn=v('<div class="c-diff-view__layout svelte-ibi4q5"><div class="c-diff-view__tree svelte-ibi4q5"><div class="c-diff-view__tree__header svelte-ibi4q5"><!> <!> <!> <!></div></div> <div class="c-diff-view__explanation svelte-ibi4q5"><!></div></div>'),Un=v('<div class="c-diff-view svelte-ibi4q5"><!> <!></div> <!>',1);function Wn(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(Ve,"$diffViewFilesMap",S),f=c(),O=c(),le=c(),j=c(),ce=c(),M=c(),K=c(),D=c();let A=E(n,"changedFiles",8),y=E(n,"agentLabel",24,()=>{}),Z=E(n,"latestUserPrompt",24,()=>{}),I=E(n,"onApplyChanges",24,()=>{}),ne=E(n,"onOpenFile",24,()=>{}),Q=E(n,"onRenderBackup",24,()=>{}),G=E(n,"preloadedExplanation",24,()=>{}),u=E(n,"isAgentFromDifferentRepo",8,!1),z=E(n,"conflictFiles",24,()=>new Set);const L=Bt(Ut.key);let m="",ue=c(!1),F=c([]),b=c([]),r=c(!1),B=c(!1),me=c(null),Ee=c(!0),_e=c({}),Ae=c([]),Me=c(!1),oe=c(!1),ge=c(!0),Re=c(new Set),Be=c(!1);const Ve=di({});let De=c({});function ze(){const s=li(e(F)),k=Object.values(e(_e)).some(Boolean);t(Ee,k),Array.from(s).forEach(C=>{zt(_e,e(_e)[C]=!e(Ee))})}async function Je(s,k,C){if(I())return Ve.update(_=>(_[s]="pending",_)),new Promise(_=>{var T;(T=I())==null||T(s,k,C).then(()=>{Ve.update(g=>(g[s]="applied",g)),_()})})}async function Ye(){const s=await L.canApplyChanges();s.canApply?je():s.hasUnstagedChanges&&t(Be,!0)}function je(){if(!I())return;L.reportApplyChangesEvent(),t(Me,!0),t(oe,!1);const{filesToApply:s,areAllPathsApplied:k}=Fi(e(F),A(),e(De));k||s.length===0?t(oe,k):_s(s,Je).then(()=>{t(Me,!1),t(oe,!0)})}function Ue(s){const k={title:"Changed Files",description:`${s.length} files were changed`,sections:[]},C=[],_=[],T=[];return s.forEach(g=>{g.old_path?g.new_path?_.push(g):T.push(g):C.push(g)}),C.length>0&&k.sections.push(at("Added files","feature",C)),_.length>0&&k.sections.push(at("Modified files","fix",_)),T.length>0&&k.sections.push(at("Deleted files","chore",T)),[k]}function at(s,k,C){const _=[];return C.forEach(T=>{const g=T.new_path||T.old_path,x=T.old_contents||"",d=T.new_contents||"",V=T.old_path?T.old_path:"",p=ui(V,T.new_path||"/dev/null",x,d,"","",{context:3}),W=`${Rt(g)}-${Rt(x+d)}`;_.push({id:W,path:g,diff:p,originalCode:x,modifiedCode:d})}),{title:s,descriptions:[],type:k,changes:_}}async function Xe(){if(!e(ue))return;if(t(r,!0),t(B,!1),t(me,null),t(b,[]),t(F,[]),e(ce))return void t(r,!1);const s=102400;let k=0;if(A().forEach(C=>{var _,T;k+=(((_=C.old_contents)==null?void 0:_.length)||0)+(((T=C.new_contents)==null?void 0:T.length)||0)}),A().length>12||k>512e3){try{t(F,Ue(A()))}catch(C){console.error("Failed to create simple explanation:",C),t(me,"Failed to create explanation for large changes.")}t(r,!1)}else try{const C=new ws(g=>qi.postMessage(g)),_=new Map,T=A().map(g=>{const x=g.new_path||g.old_path,d=g.old_contents||"",V=g.new_contents||"",p=`${Rt(x)}-${Rt(d+V)}`;return _.set(p,{old_path:g.old_path,new_path:g.new_path,old_contents:d,new_contents:V,change_type:g.change_type}),{id:p,old_path:g.old_path,new_path:g.new_path,change_type:g.change_type}});try{const g=T.length===1;let x=[];g?x=T.map(d=>({path:d.new_path||d.old_path,changes:[{id:d.id,path:d.new_path||d.old_path,diff:`File: ${d.new_path||d.old_path}
Change type: ${d.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):x=(await C.send({type:"get-diff-group-changes-request",data:{changedFiles:T,changesById:!0,apikey:m}},3e4)).data.groupedChanges,t(b,x.map(d=>({path:d.path,changes:d.changes.map(V=>{if(V.id&&_.has(V.id)){const p=_.get(V.id);let W=V.diff;return W&&!W.startsWith("File:")||(W=ui(p.old_path||"",p.new_path||"",p.old_contents||"",p.new_contents||"")),{...V,diff:W,old_path:p.old_path,new_path:p.new_path,old_contents:p.old_contents,new_contents:p.new_contents,change_type:p.change_type,originalCode:p.old_contents||"",modifiedCode:p.new_contents||""}}return V})})))}catch(g){console.error("Failed to group changes with LLM, falling back to simple grouping:",g);try{const x=T.map(d=>{if(d.id&&_.has(d.id)){const V=_.get(d.id);return{...d,old_path:V.old_path,new_path:V.new_path,old_contents:V.old_contents||"",new_contents:V.new_contents||"",change_type:V.change_type}}return d});t(F,Ue(x)),t(b,e(F)[0].sections.map(d=>({path:d.title,changes:d.changes}))),t(B,!1)}catch(x){console.error("Failed to create simple explanation:",x),t(me,"Failed to group changes. Please try again.")}}if(t(r,!1),!e(b)||e(b).length===0)throw new Error("Failed to group changes");if(!e(F)||e(F).length===0){t(F,function(x){const d={title:"Loading...",description:"",sections:[]};return x.forEach(V=>{const p=V.changes.map(Y=>{if(Y.id)return Y;const ee=Rt(Y.path),w=Rt(Y.originalCode+Y.modifiedCode);return{...Y,id:`${ee}-${w}`}}),W={title:V.path,descriptions:[],type:"other",changes:p};d.sections.push(W)}),[d]}(e(b)));const g=e(F)[0].sections.map(x=>({path:x.title,changes:x.changes.map(d=>{var Y,ee,w;const V=((Y=d.originalCode)==null?void 0:Y.length)||0,p=((ee=d.modifiedCode)==null?void 0:ee.length)||0,W=((w=d.diff)==null?void 0:w.length)||0;return V>s||p>s||W>s?{id:d.id,path:d.path,diff:`File: ${d.path}
Content too large to include in explanation request (${Math.max(V,p,W)} bytes)`,originalCode:V>s?`[File content too large: ${V} bytes]`:d.originalCode,modifiedCode:p>s?`[File content too large: ${p} bytes]`:d.modifiedCode}:{id:d.id,path:d.path,diff:d.diff,originalCode:d.originalCode,modifiedCode:d.modifiedCode}})}));t(B,!0);try{const{explanation:x,error:d}=await L.getDescriptions(g,m);if(d==="Token limit exceeded")return t(F,Ue(A())),t(r,!1),void t(B,!1);x&&x.length>0&&x.forEach((V,p)=>{V.sections&&V.sections.forEach((W,Y)=>{W.changes&&W.changes.forEach(ee=>{const w=e(F)[p];if(w&&w.sections){const X=w.sections[Y];if(X&&X.changes){const H=X.changes.find(re=>re.id===ee.id);H&&(ee.originalCode=H.originalCode,ee.modifiedCode=H.modifiedCode,ee.diff=H.diff)}}})})}),t(F,x)}catch(x){console.error("Failed to get descriptions, using skeleton explanation:",x)}}e(F).length===0&&t(me,"Failed to generate explanation.")}catch(C){console.error("Failed to get explanation:",C),t(me,C instanceof Error?C.message:"An error occurred while generating the explanation.")}finally{t(r,!1),t(B,!1)}}ei(()=>{const s=localStorage.getItem("anthropic_apikey");s&&(m=s),t(ue,!0)});let ct=c(""),Oe=c("Apply all changes locally");N(()=>(a(A()),h()),()=>{A()&&Ve.set(A().reduce((s,k)=>{const C=k.new_path||k.old_path;return s[C]=h()[C]??"none",s},{}))}),N(()=>e(_e),()=>{t(f,Object.values(e(_e)).some(Boolean))}),N(()=>a(A()),()=>{t(M,JSON.stringify(A()))}),N(()=>(e(ue),e(M),e(ct),a(G())),()=>{e(ue)&&e(M)&&e(M)!==e(ct)&&(t(ct,e(M)),G()&&G().length>0?(t(F,G()),t(r,!1),t(B,!1)):Xe(),t(Me,!1),t(oe,!1),t(De,{}))}),N(()=>(e(F),e(De)),()=>{e(F)&&e(F).length>0&&e(F).flatMap(s=>s.sections||[]).flatMap(s=>s.changes).forEach(s=>{e(De)[s.path]||zt(De,e(De)[s.path]=s.modifiedCode)})}),N(()=>e(F),()=>{t(O,JSON.stringify(e(F)))}),N(()=>(e(F),e(_e),e(Ee)),()=>{if(e(F)&&e(F).length>0){const s=li(e(F));Array.from(s).forEach(_=>{e(_e)[_]===void 0&&zt(_e,e(_e)[_]=!e(Ee))});const k=Object.keys(e(_e)).filter(_=>e(_e)[_]),C=Array.from(s);C.length>0&&t(Ee,!C.some(_=>k.includes(_)))}}),N(()=>(e(O),h(),e(F)),()=>{t(le,(()=>{if(e(O)&&h()){const s=li(e(F));return s.size!==0&&Array.from(s).some(k=>h()[k]!=="applied")}return!1})())}),N(()=>h(),()=>{t(oe,Object.keys(h()).every(s=>h()[s]==="applied"))}),N(()=>h(),()=>{t(j,Object.keys(h()).filter(s=>h()[s]==="pending"))}),N(()=>(e(F),a(A()),e(De)),()=>{(async function(s,k,C){const{filesToApply:_}=Fi(s,k,C),T=new Set;for(const g of _)(await L.previewApplyChanges(g.path,g.originalCode,g.newCode)).hasConflicts&&T.add(g.path);t(Re,T)})(e(F),A(),e(De))}),N(()=>a(A()),()=>{t(ce,A().length===0)}),N(()=>(e(O),e(oe),e(F),h()),()=>{if(e(O)&&e(oe)){const s=li(e(F));Array.from(s).every(k=>h()[k]==="applied")||t(oe,!1)}}),N(()=>(e(oe),a(z())),()=>{t(K,e(oe)&&z().size>0)}),N(()=>(a(u()),e(Me),e(oe),e(j),e(le)),()=>{t(D,u()||e(Me)||e(oe)||e(j).length>0||!e(le))}),N(()=>(e(D),a(u()),e(Me),e(K),e(oe),e(j),e(le)),()=>{e(D)?u()?t(Oe,"Cannot apply changes from a different repository locally"):e(Me)?t(Oe,"Applying changes..."):e(K)?t(Oe,"All changes applied, but conflicts need to be resolved manually"):e(oe)?t(Oe,"All changes applied"):e(j).length>0?t(Oe,"Waiting for changes to apply"):e(le)||t(Oe,"No changes to apply"):t(Oe,"Apply all changes locally")}),Mt(),st();var be=Un(),We=we(be),$=l(We),P=s=>{var k=bn(),C=l(k);xi(C,{});var _=q(C),T=q(_);_t(T,{variant:"ghost",size:1,$$events:{click:Xe},children:(d,V)=>{var p=de("Retry");i(d,p)},$$slots:{default:!0}});var g=q(T,2),x=d=>{_t(d,{variant:"ghost",size:1,$$events:{click(...V){var p;(p=Q())==null||p.apply(this,V)}},children:(V,p)=>{var W=de("Render as list");i(V,W)},$$slots:{default:!0}})};R(g,d=>{Q()&&d(x)}),ye(()=>Ze(_,` ${e(me)??""} `)),i(s,k)};R($,s=>{e(me)&&s(P)});var J=q($,2),ie=s=>{var k=kn(),C=l(k);qe(C,{size:2,color:"secondary",children:(_,T)=>{var g=de("No files changed");i(_,g)},$$slots:{default:!0}}),i(s,k)},ae=s=>{var k=Bn(),C=l(k),_=l(C),T=l(_),g=w=>{var X=An(),H=we(X);qe(H,{size:1,class:"c-diff-view__tree__header__label",children:(te,ve)=>{var fe=de("Changes from agent");i(te,fe)},$$slots:{default:!0}});var re=q(H,2);qe(re,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(te,ve)=>{var fe=de();ye(()=>Ze(fe,y())),i(te,fe)},$$slots:{default:!0}}),i(w,X)};R(T,w=>{y()&&Z()!==y()&&w(g)});var x=q(T,2),d=w=>{var X=zn(),H=we(X);qe(H,{size:1,class:"c-diff-view__tree__header__label",children:(te,ve)=>{var fe=de("Last user prompt");i(te,fe)},$$slots:{default:!0}});var re=q(H,2);qe(re,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(te,ve)=>{var fe=de();ye(()=>Ze(fe,Z())),i(te,fe)},$$slots:{default:!0}}),i(w,X)};R(x,w=>{Z()&&w(d)});var V=q(x,2);qe(V,{size:1,class:"c-diff-view__tree__header__label",children:(w,X)=>{var H=de("Changed files");i(w,H)},$$slots:{default:!0}}),Zi(q(V,2),{get changedFiles(){return A()}});var p=q(C,2),W=l(p),Y=w=>{var X=qn(),H=we(X),re=l(H);const te=se(()=>e(Me)?"Applying changes...":e(oe)?"All changes applied":e(le)?"Apply all changes":"No changes to apply");St(re,{get content(){return e(te)},children:(ve,fe)=>{const U=se(()=>(e(Me),e(oe),e(j),e(le),o(()=>e(Me)||e(oe)||e(j).length>0||!e(le))));_t(ve,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(U)},$$events:{click:Ye},children:(ke,He)=>{var Ce=Ft(),Pe=we(Ce),vt=ht=>{var Lt=Fn(),qt=l(Lt);oi(qt,{size:1,useCurrentColor:!0});var ft=q(qt,2);qe(ft,{size:2,children:(et,wt)=>{var Et=de("Applying...");i(et,Et)},$$slots:{default:!0}}),i(ht,Lt)},lt=(ht,Lt)=>{var qt=et=>{var wt=Mn(),Et=l(wt);qe(Et,{size:2,children:(Dt,Gt)=>{var Ht=xn(),ii=q(we(Ht));mi(ii,{iconName:"check"}),i(Dt,Ht)},$$slots:{default:!0}}),i(et,wt)},ft=et=>{var wt=Ln(),Et=q(we(wt)),Dt=l(Et);ci(Dt,{}),i(et,wt)};R(ht,et=>{e(oe)?et(qt):et(ft,!1)},Lt)};R(Pe,ht=>{e(Me)?ht(vt):ht(lt,!1)}),i(ke,Ce)},$$slots:{default:!0}})},$$slots:{default:!0}}),function(ve,fe){let U=E(fe,"count",8,2);var ke=pn();dt(ke,5,()=>(a(U()),o(()=>Array(U()))),mt,(He,Ce,Pe)=>{var vt=fn(),lt=l(vt),ht=q(l(lt),2),Lt=ft=>{var et=dn();i(ft,et)};R(ht,ft=>{Pe===0&&ft(Lt)});var qt=q(lt,2);dt(qt,0,()=>Array(2),mt,(ft,et,wt,Et)=>{var Dt=vn(),Gt=q(l(Dt),2);dt(Gt,4,()=>Array(2),mt,(Ht,ii,Le,xe)=>{var yt=cn();i(Ht,yt)}),i(ft,Dt)}),i(He,vt)}),i(ve,ke)}(q(H,2),{count:2}),i(w,X)},ee=(w,X)=>{var H=re=>{var te=Ft(),ve=we(te);dt(ve,1,()=>e(F),mt,(fe,U,ke)=>{var He=In();Qe(He,"id",`section-${ke}`);var Ce=l(He),Pe=l(Ce),vt=l(Pe),lt=l(vt),ht=Le=>{var xe=En();i(Le,xe)},Lt=Le=>{var xe=de();ye(()=>Ze(xe,(e(U),o(()=>e(U).title)))),i(Le,xe)};R(lt,Le=>{e(B),e(U),o(()=>e(B)&&e(U).title==="Loading...")?Le(ht):Le(Lt,!1)});var qt=q(vt,2),ft=l(qt),et=Le=>{var xe=Dn();i(Le,xe)},wt=Le=>{_i(Le,{get markdown(){return e(U),o(()=>e(U).description)}})};R(ft,Le=>{e(B),e(U),o(()=>e(B)&&e(U).description==="")?Le(et):Le(wt,!1)});var Et=q(Pe,2),Dt=Le=>{var xe=Vn(),yt=l(xe);_t(yt,{variant:"ghost-block",color:"neutral",size:2,$$events:{click:ze},children:(gt,Jt)=>{var $t=Ft(),Pt=we($t),Nt=pt=>{var ot=Pn(),Ke=we(ot);Ji(Ke,{}),i(pt,ot)},Zt=pt=>{var ot=Nn(),Ke=we(ot);ls(Ke),i(pt,ot)};R(Pt,pt=>{e(f)?pt(Zt,!1):pt(Nt)}),i(gt,$t)},$$slots:{default:!0}});var ut=q(yt,2);St(ut,{get content(){return e(Oe)},children:(gt,Jt)=>{_t(gt,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(D)},$$events:{click:Ye},children:($t,Pt)=>{var Nt=Ft(),Zt=we(Nt),pt=Ke=>{var Ct=On(),Ne=l(Ct);oi(Ne,{size:1,useCurrentColor:!0});var he=q(Ne,2);qe(he,{size:2,children:(Se,Ge)=>{var tt=de("Applying...");i(Se,tt)},$$slots:{default:!0}}),i(Ke,Ct)},ot=(Ke,Ct)=>{var Ne=Se=>{var Ge=Sn(),tt=l(Ge);qe(tt,{size:2,children:(Fe,Ie)=>{var rt=de("Applied");i(Fe,rt)},$$slots:{default:!0}});var bt=q(tt,2),Vt=Fe=>{Ai(Fe,{slot:"rightIcon"})},Tt=Fe=>{mi(Fe,{iconName:"check"})};R(bt,Fe=>{e(K)?Fe(Vt):Fe(Tt,!1)}),i(Se,Ge)},he=Se=>{var Ge=Zn(),tt=q(we(Ge)),bt=l(tt);ci(bt,{}),i(Se,Ge)};R(Ke,Se=>{e(oe)?Se(Ne):Se(he,!1)},Ct)};R(Zt,Ke=>{e(Me)?Ke(pt):Ke(ot,!1)}),i($t,Nt)},$$slots:{default:!0}})},$$slots:{default:!0}}),i(Le,xe)};R(Et,Le=>{ke===0&&Le(Dt)});var Gt=q(Ce,2),Ht=Le=>{const xe=se(()=>e(oe)?z():e(Re));(function(yt,ut){it(ut,!1);let gt=E(ut,"files",8),Jt=E(ut,"hasAppliedAll",8),$t=E(ut,"onOpenFile",24,()=>{});st();var Pt=mn(),Nt=l(Pt);fs(Nt,{includeBackground:!1,children:(Zt,pt)=>{var ot=gn(),Ke=we(ot),Ct=l(Ke),Ne=l(Ct);Ai(Ne,{});var he=q(Ct,2),Se=l(he),Ge=q(Ke,2),tt=l(Ge),bt=Fe=>{var Ie=de("The following files have merge conflicts that need to be resolved manually.");i(Fe,Ie)},Vt=Fe=>{var Ie=de(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`);i(Fe,Ie)};R(tt,Fe=>{Jt()?Fe(bt):Fe(Vt,!1)});var Tt=q(Ge,2);dt(Tt,1,gt,mt,(Fe,Ie)=>{var rt=un(),kt=l(rt);St(kt,{get content(){return e(Ie)},nested:!0,children:(It,fi)=>{var At=hn(),Kt=we(At);Di(Kt,{get filename(){return e(Ie)}});var si=q(Kt,2);ys(si,{get filepath(){return e(Ie)}}),i(It,At)},$$slots:{default:!0}});var Yt=q(kt,2);St(Yt,{content:"Open file",children:(It,fi)=>{yi(It,{size:1,variant:"ghost-block",color:"neutral",$$events:{click:()=>{var At;return(At=$t())==null?void 0:At(e(Ie))}},children:(At,Kt)=>{Ei(At,{})},$$slots:{default:!0}})},$$slots:{default:!0}}),i(Fe,rt)}),ye(()=>Ze(Se,`Conflicts (${a(gt()),o(()=>gt().size)??""})`)),i(Zt,ot)},$$slots:{default:!0}}),i(yt,Pt),nt()})(Le,{get files(){return e(xe)},get hasAppliedAll(){return e(oe)},get onOpenFile(){return ne()}})};R(Gt,Le=>{e(oe),a(z()),e(Re),o(()=>(e(oe)&&z().size>0||!e(oe)&&e(Re).size>0)&&ke===0)&&Le(Ht)});var ii=q(Gt,2);dt(ii,1,()=>(e(U),o(()=>e(U).sections||[])),mt,(Le,xe,yt)=>{var ut=Hn();Qe(ut,"id",`subsection-${ke}-${yt}`);var gt=l(ut),Jt=l(gt),$t=l(Jt);(function(Ne,he){it(he,!1);const Se=c();let Ge=E(he,"type",8);const tt={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};N(()=>a(Ge()),()=>{t(Se,tt[Ge()]??tt.other)}),Mt(),st();const bt=se(()=>`This is a ${Ge()} change`),Vt=se(()=>(a(Ot),o(()=>[Ot.Hover])));St(Ne,{get content(){return e(bt)},get triggerOn(){return e(Vt)},children:(Tt,Fe)=>{var Ie=rn(),rt=l(Ie),kt=Yt=>{var It=Ft(),fi=we(It);dt(fi,1,()=>(e(Se),o(()=>e(Se).paths)),mt,(At,Kt)=>{var si=on();ye(()=>Qe(si,"d",e(Kt))),i(At,si)}),i(Yt,It)};R(rt,Yt=>{e(Se)&&Yt(kt)}),i(Tt,Ie)},$$slots:{default:!0}}),nt()})(l($t),{get type(){return e(xe),o(()=>e(xe).type)}});var Pt=q($t,2),Nt=l(Pt),Zt=Ne=>{var he=Tn();i(Ne,he)},pt=Ne=>{var he=de();ye(()=>Ze(he,(e(xe),o(()=>e(xe).title)))),i(Ne,he)};R(Nt,Ne=>{e(B),e(xe),o(()=>e(B)&&e(xe).descriptions.length===0)?Ne(Zt):Ne(pt,!1)});var ot=q(Pt,2),Ke=Ne=>{var he=Rn(),Se=l(he);xi(Se,{});var Ge=q(Se);ye(()=>Ze(Ge,` ${e(xe),o(()=>e(xe).warning)??""}`)),i(Ne,he)};R(ot,Ne=>{e(B),e(xe),o(()=>!e(B)&&e(xe).warning)&&Ne(Ke)});var Ct=q(gt,2);dt(Ct,5,()=>(e(xe),o(()=>e(xe).changes)),Ne=>Ne.id,(Ne,he)=>{var Se=jn(),Ge=l(Se);const tt=se(()=>(e(_e),e(he),e(Ee),o(()=>e(_e)[e(he).path]!==void 0?!e(_e)[e(he).path]:e(Ee)))),bt=se(()=>(h(),e(he),o(()=>h()[e(he).path]==="pending"))),Vt=se(()=>(h(),e(he),o(()=>h()[e(he).path]==="applied"))),Tt=se(()=>ne()?()=>ne()(e(he).path):void 0);ri(Oi(Ge,{get path(){return e(he),o(()=>e(he).path)},get change(){return e(he)},get descriptions(){return e(xe),o(()=>e(xe).descriptions)},get isExpandedDefault(){return e(tt)},get isApplying(){return e(bt)},get hasApplied(){return e(Vt)},onCodeChange:Fe=>{(function(Ie,rt){zt(De,e(De)[Ie]=rt)})(e(he).path,Fe)},onApplyChanges:()=>{Je(e(he).path,e(he).originalCode,e(he).modifiedCode)},get onOpenFile(){return e(Tt)},get isAgentFromDifferentRepo(){return u()},get isCollapsed(){return e(_e)[e(he).path]},set isCollapsed(Fe){zt(_e,e(_e)[e(he).path]=Fe)},get areDescriptionsVisible(){return e(ge)},set areDescriptionsVisible(Fe){t(ge,Fe)},$$legacy:!0}),(Fe,Ie,rt,kt)=>zt(Ae,e(Ae)[100*Ie+10*rt+kt.path.length%10]=Fe),(Fe,Ie,rt)=>{var kt;return(kt=e(Ae))==null?void 0:kt[100*Fe+10*Ie+rt.path.length%10]},()=>[ke,yt,e(he)]),i(Ne,Se)}),i(Le,ut)}),i(fe,He)}),i(re,te)};R(w,re=>{e(F),o(()=>e(F)&&e(F).length>0)&&re(H)},X)};R(W,w=>{e(r),e(b),o(()=>e(r)&&e(b).length===0)?w(Y):w(ee,!1)}),i(s,k)};R(J,s=>{e(ce)?s(ie):s(ae,!1)}),function(s,k){it(k,!1);let C=E(k,"showModal",12,!1),_=E(k,"applyAllChanges",8);const T=Bt(Ut.key);let g=c(void 0),x=c(!1);async function d(){if(t(x,!0),!await T.stashUnstagedChanges())return t(g,"Failed to stash changes. Please manually stash or commit your unstaged changes."),void t(x,!1);await new Promise(p=>setTimeout(p,1500)),t(g,void 0),C(!1),_()(),t(x,!1)}function V(){C(!1),t(g,void 0)}st(),$s(s,{get show(){return C()},title:"Unstaged changes",$$events:{cancel:V},children:(p,W)=>{var Y=wn(),ee=l(Y);qe(ee,{children:(H,re)=>{var te=_n(),ve=q(we(te));as(ve,{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}),i(H,te)},$$slots:{default:!0}});var w=q(ee,2),X=H=>{qe(H,{children:(re,te)=>{var ve=de();ye(()=>Ze(ve,e(g))),i(re,ve)},$$slots:{default:!0}})};R(w,H=>{e(g)&&H(X)}),i(p,Y)},$$slots:{default:!0,footer:(p,W)=>{var Y=Cn(),ee=l(Y);const w=se(()=>!!e(g)||e(x));_t(ee,{variant:"solid",color:"accent",get disabled(){return e(w)},$$events:{click:d},children:(H,re)=>{var te=$n(),ve=we(te),fe=He=>{var Ce=yn(),Pe=l(Ce);oi(Pe,{size:1}),i(He,Ce)};R(ve,He=>{e(x)&&He(fe)});var U=q(ve,2);let ke;ye(He=>ke=jt(U,1,"c-unstaged-changes-modal__stash-button-text svelte-9eyy34",null,ke,He),[()=>({loading:e(x)})],se),i(H,te)},$$slots:{default:!0}});var X=q(ee,2);_t(X,{variant:"solid",color:"neutral",get disabled(){return e(x)},$$events:{click:V},children:(H,re)=>{var te=de("Abort");i(H,te)},$$slots:{default:!0}}),i(p,Y)}}}),nt()}(q(We,2),{applyAllChanges:je,get showModal(){return e(Be)},set showModal(s){t(Be,s)},$$legacy:!0}),i(pe,be),nt(),$e()}var Gn=v('<div class="file-explorer-contents svelte-5tfpo4"><!></div>'),Jn=v('<div class="diff-page svelte-5tfpo4"><div class="file-explorer-main svelte-5tfpo4"><!></div></div>');function Yn(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(A,"$diffModel",S);let f=E(n,"changedFiles",24,()=>[]),O=E(n,"pendingFiles",24,()=>[]),le=E(n,"appliedFiles",24,()=>[]),j=E(n,"agentLabel",24,()=>{}),ce=E(n,"latestUserPrompt",24,()=>{}),M=E(n,"isAgentFromDifferentRepo",8,!1),K=c(new Set);const D=Bt(Ut.key),A=Bt(vi.key);let y=c("summary");const Z=async(z,L,m)=>{const{success:ue,hasConflicts:F}=await D.applyChanges(z,L,m);ue&&F&&t(K,new Set([...e(K),z]))},I=z=>D.openFile(z);(function(z){z.subscribe(L=>{if(L){const m=document.getElementById(wi(L));m&&m.scrollIntoView({behavior:"smooth",block:"center"})}})})(function(z=null){const L=di(z);return hi(Pi,L),L}(null)),st();var ne=Jn(),Q=l(ne),G=l(Q),u=z=>{var L=Gn(),m=l(L);Yi(m,()=>(h(),o(()=>h().opts)),ue=>{var F=Ft(),b=we(F),r=me=>{ln(me,{get changedFiles(){return f()},onApplyChanges:Z,onOpenFile:I,get pendingFiles(){return O()},get appliedFiles(){return le()}})},B=me=>{const Ee=se(()=>(h(),o(()=>{var _e,Ae;return(Ae=(_e=h())==null?void 0:_e.opts)==null?void 0:Ae.preloadedExplanation})));Wn(me,{get changedFiles(){return f()},onApplyChanges:Z,onOpenFile:I,get agentLabel(){return j()},get latestUserPrompt(){return ce()},onRenderBackup:()=>{t(y,"changedFiles")},get preloadedExplanation(){return e(Ee)},get isAgentFromDifferentRepo(){return M()},get conflictFiles(){return e(K)}})};R(b,me=>{e(y)==="changedFiles"?me(r):me(B,!1)}),i(ue,F)}),i(z,L)};R(G,z=>{f()&&z(u)}),i(pe,ne),nt(),$e()}var Kn=v('<div class="l-center svelte-ccste2"><!> <p>Loading diff view...</p></div>'),Qn=v('<div class="l-main svelte-ccste2"><!></div>');Ii(function(pe,n){it(n,!1);const[S,$e]=Wt(),h=()=>xt(le,"$remoteAgentDiffModel",S),f=c();let O=new Ui(qi),le=new vi(O);O.registerConsumer(le);let j=new Ut(O);hi(Ut.key,j),hi(vi.key,le),ei(()=>(le.onPanelLoaded(),()=>{O.dispose()})),N(()=>h(),()=>{t(f,h().opts)}),Mt(),st(),ti("message",Hi,function(...ce){var M;(M=O.onMessageFromExtension)==null||M.apply(this,ce)}),cs.Root(pe,{children:(ce,M)=>{var K=Qn(),D=l(K),A=Z=>{const I=se(()=>o(()=>j.applyingFilePaths||[])),ne=se(()=>o(()=>j.appliedFilePaths||[])),Q=se(()=>(e(f),o(()=>e(f).isAgentFromDifferentRepo||!1)));Yn(Z,{get changedFiles(){return e(f),o(()=>e(f).changedFiles)},get agentLabel(){return e(f),o(()=>e(f).sessionSummary)},get latestUserPrompt(){return e(f),o(()=>e(f).userPrompt)},get pendingFiles(){return e(I)},get appliedFiles(){return e(ne)},get isAgentFromDifferentRepo(){return e(Q)}})},y=Z=>{var I=Kn(),ne=l(I);oi(ne,{size:1}),i(Z,I)};R(D,Z=>{e(f)?Z(A):Z(y,!1)}),i(ce,K)},$$slots:{default:!0}}),nt(),$e()},{target:document.getElementById("app")});
