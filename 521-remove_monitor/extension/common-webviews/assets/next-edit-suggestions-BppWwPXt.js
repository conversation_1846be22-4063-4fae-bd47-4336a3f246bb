const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-IW1pin9L.css","./NextEditSuggestions-DzNb_2dY.js","./SpinnerAugment-AffdR7--.js","./SpinnerAugment-DoxdFmoV.css","./next-edit-types-904A5ehg.js","./IconButtonAugment-DVt24OaC.js","./IconButtonAugment-B4afvB2A.css","./IconFilePath-Bz0iFBtq.js","./LanguageIcon-CVD96Tjf.js","./LanguageIcon-D78BqCXT.css","./IconFilePath-BVaLv7mP.css","./async-messaging-DXXiLgz5.js","./Drawer-jmAZQpgu.js","./index-oHUUsc-1.js","./ellipsis-BHLqUIzX.js","./Drawer-u8LRIFRf.css","./keypress-DD1aQVr0.js","./VSCodeCodicon-CM9n-Tfg.js","./VSCodeCodicon-DVaocTud.css","./svelte-component-BzMfvILK.js","./monaco-render-utils-DfwV7QLY.js","./toggleHighContrast-Cb9MCs64.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./index-ZbZe59K6.js","./index-C5DcjNTh.js","./index-BlHvDt2c.css","./isObjectLike-D6uePTe3.js","./ButtonAugment-K-zrKZyw.js","./ButtonAugment-BcSV_kHI.css","./NextEditSuggestions-Q98kphIR.css"])))=>i.map(i=>d[i]);
import{A as E,C as o,m as Z,a6 as ee,ak as ae,D as O,F as te,G as ie,b as r,I as F,Y as ne,J as b,t as l,M as c,T as p,V as k,Q as _,W as P,_ as G,a7 as oe,ay as re,L as se,az as le}from"./SpinnerAugment-AffdR7--.js";import"./design-system-init-BpU1o6o4.js";import{_ as q}from"./preload-helper-Dv6uf1Os.js";import{a as ge}from"./await-637P_Cby.js";import{t as T,a as C}from"./index-oHUUsc-1.js";import{A as x}from"./augment-logo-Dea9S5F6.js";import{M as de}from"./index-ZbZe59K6.js";import"./index-C5DcjNTh.js";const W={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};var me=b('<div class="l-component svelte-1foy1hj"><!></div>'),ce=b('<code class="svelte-1foy1hj"> </code>'),he=b('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container l-loader-error-message svelte-1foy1hj"><!> <!></div></div>'),ue=b('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container svelte-1foy1hj"><!> <!></div></div>');le(function(B,V){E(V,!1);const H=async()=>(await q(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await q(async()=>{const{default:I}=await import("./NextEditSuggestions-DzNb_2dY.js");return{default:I}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]),import.meta.url)).default);O(),de.Root(B,{children:(I,ve)=>{(function(J,t){E(t,!1);let N=o(t,"minDisplayTime",8,1e3),K=o(t,"loader",8),U=o(t,"props",8),z=o(t,"title",8,"Augment Code"),Q=o(t,"randomize",8,!0),$=o(t,"retryCount",8,3),A=o(t,"loadingMessages",24,()=>W.messages),L=o(t,"errorMessages",24,()=>W.errors),S=o(t,"errorMessage",8,"An error occurred while loading. Please try again later."),h=A().slice(1),j=Z(A()[0]),u="loading",M=new AbortController;async function R(i=0){try{const[a]=await Promise.all([K()(),(n=N(),e=M.signal,new Promise(g=>{const v=setTimeout(g,n);e&&e.addEventListener("abort",()=>{clearTimeout(v),g()})}))]);return a}catch(a){if(console.error("Failed to load component",a),u="retry",i===0&&(h=[...L()]),$()&&i<=$())return await R(i+1);throw u="error",new Error("Failed to load component after retrying. Please try again later.")}var n,e}ee(()=>M.abort()),ae(async function(){h.length===0&&(h=[...u==="retry"?L():A()]),ne(j,u==="error"?S():h.splice(u!=="retry"&&Q()?Math.floor(Math.random()*h.length):0,1)[0]??"")}),O();var D=te(),X=ie(D);ge(X,()=>se(R),i=>{var n=ue(),e=l(n),a=l(e);x(a);var g=c(a,2);p(g,{size:2,children:(f,d)=>{var s=k();_(()=>P(s,z())),r(f,s)},$$slots:{default:!0}});var v=c(e,2),y=l(v);re(y,{});var w=c(y,2);p(w,{size:1,color:"secondary",children:(f,d)=>{var s=k();_(()=>P(s,G(j))),r(f,s)},$$slots:{default:!0}}),T(3,n,()=>C),r(i,n)},(i,n)=>{var e=me(),a=l(e);G(n)(a,oe(U)),T(3,e,()=>C),r(i,e)},(i,n)=>{var e=he(),a=l(e),g=l(a);x(g);var v=c(g,2);p(v,{size:3,children:(d,s)=>{var m=k();_(()=>P(m,z())),r(d,m)},$$slots:{default:!0}});var y=c(a,2),w=l(y);p(w,{size:3,children:(d,s)=>{var m=k("An Error Occurred.");r(d,m)},$$slots:{default:!0}});var f=c(w,2);p(f,{size:1,children:(d,s)=>{var m=ce(),Y=l(m);_(()=>P(Y,S())),r(d,m)},$$slots:{default:!0}}),T(3,e,()=>C),r(i,e)}),r(J,D),F()})(I,{loader:H,props:{}})},$$slots:{default:!0}}),F()},{target:document.getElementById("app")});
