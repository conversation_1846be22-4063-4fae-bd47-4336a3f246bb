.c-button--icon.svelte-14satks svg,.c-button--content.svelte-14satks svg{width:var(--button-icon-size);height:var(--button-icon-size);display:block}.c-button--icon.svelte-14satks svg:not([data-ds-icon=fa]),.c-button--content.svelte-14satks svg:not([data-ds-icon=fa]){aspect-ratio:1/1}.c-button--icon.svelte-14satks svg{fill:var(--icon-color, currentColor)}.c-button--content.svelte-14satks{--button-icon-size: var(--icon-size, 16px);display:inline-flex;align-items:center;justify-content:center;-webkit-user-select:none;user-select:none;vertical-align:top;padding:var(--base-btn-padding-vertical) var(--base-btn-padding-horizontal);gap:var(--base-btn-gap)}.c-button--icon.svelte-14satks{display:flex;align-items:center;justify-content:center}.c-button--icon.svelte-14satks:not(:has(>*)){display:contents}.c-button--text.svelte-14satks .c-text{display:flex;align-items:center;justify-content:center;line-height:1}.c-button--text.svelte-14satks:empty,.c-button--text.svelte-14satks:has(>:where(.svelte-14satks):empty){display:none}.c-button--size-3.svelte-14satks{--button-icon-size: var(--icon-size, 18px)}.c-button--size-4.svelte-14satks{--button-icon-size: var(--icon-size, 20px)}
