import{z as oa,A as la,B as ta,C as t,D as ra,F as z,G as B,H as n,b as r,I as da,J as m,K as ia,L as y,t as v,M as I,N as q,O as J,P as C,Q as K,R as ca,S as na,T as va,V as fa,W as wa}from"./SpinnerAugment-AffdR7--.js";import{b as N,a as ha}from"./IconButtonAugment-DVt24OaC.js";import{C as ma,s as O}from"./CardAugment-CB88N7dm.js";import{t as pa}from"./CollapseButtonAugment-DgGSnbBS.js";var ua=m('<div class="c-modal-header svelte-1hwqfwo"><!></div>'),ba=m('<div class="c-modal-body svelte-1hwqfwo"><!></div>'),ka=m('<div class="c-modal-footer svelte-1hwqfwo"><!></div>'),ya=m('<div class="c-modal-content svelte-1hwqfwo"><!> <!> <!></div>'),qa=m('<div class="c-modal-backdrop svelte-1hwqfwo" role="presentation"><div class="c-modal svelte-1hwqfwo" role="dialog" aria-modal="true" tabindex="0"><!></div></div>');function Ba(P,a){const f=oa(a);la(a,!1);const p=ta();let D=t(a,"show",8,!1),u=t(a,"title",8,""),Q=t(a,"maxWidth",8,"400px"),R=t(a,"preventBackdropClose",8,!1),S=t(a,"preventEscapeClose",8,!1),E=t(a,"ariaLabelledBy",8,"modal-title"),M=t(a,"oncancel",24,()=>{}),T=t(a,"onbackdropClick",24,()=>{}),V=t(a,"onkeydown",24,()=>{});function j(){var e,o;R()||(p("cancel"),(e=M())==null||e()),p("backdropClick"),(o=T())==null||o()}function U(e){var o,l;e.key!=="Escape"||S()||(e.preventDefault(),p("cancel"),(o=M())==null||o()),p("keydown",e),(l=V())==null||l(e)}ra();var W=z(),X=B(W),Y=e=>{var o=qa(),l=v(o),Z=v(l);ma(Z,{variant:"soft",size:3,children:(c,A)=>{var w=ya(),F=v(w),_=s=>{var d=ua(),h=v(d),x=i=>{var k=z(),$=B(k);q($,a,"header",{},null),r(i,k)},b=(i,k)=>{var $=g=>{va(g,{get id(){return E()},size:3,weight:"bold",color:"primary",children:(sa,Ca)=>{var H=fa();K(()=>wa(H,u())),r(sa,H)},$$slots:{default:!0}})};n(i,g=>{u()&&g($)},k)};n(h,i=>{y(()=>f.header)?i(x):i(b,!1)}),r(s,d)};n(F,s=>{ia(u()),y(()=>u()||f.header)&&s(_)});var G=I(F,2),L=s=>{var d=ba(),h=v(d);q(h,a,"body",{},x=>{var b=z(),i=B(b);q(i,a,"default",{},null),r(x,b)}),r(s,d)};n(G,s=>{y(()=>f.body||f.default)&&s(L)});var aa=I(G,2),ea=s=>{var d=ka(),h=v(d);q(h,a,"footer",{},null),r(s,d)};n(aa,s=>{y(()=>f.footer)&&s(ea)}),r(c,w)},$$slots:{default:!0}}),J(()=>C("click",l,O(function(c){N.call(this,a,c)}))),J(()=>C("keydown",l,O(function(c){N.call(this,a,c)}))),ha(l,(c,A)=>{var w;return(w=pa)==null?void 0:w(c,A)},()=>({enabled:D()})),K(()=>{ca(l,"aria-labelledby",E()),na(l,`max-width: ${Q()??""}`)}),C("click",o,j),C("keydown",o,U),r(e,o)};n(X,e=>{D()&&e(Y)}),r(P,W),da()}export{Ba as M};
