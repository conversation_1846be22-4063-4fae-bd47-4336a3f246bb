import{l as F,A as at,C as i,m as g,X as q,$ as st,D as lt,J as O,F as W,G as N,H as U,b as $,Y as c,_ as s,a3 as vt,a7 as tt,N as H,M as G,t as Y,I as nt,L as it,K as R,Z as rt,z as ht,ak as dt,a as ft,a1 as pt,am as gt,O as p,P as m,Q as $t,R as mt,W as yt}from"./SpinnerAugment-AffdR7--.js";import{I as wt,b as l,a as xt}from"./IconButtonAugment-DVt24OaC.js";import{T as bt,a as et}from"./CardAugment-CB88N7dm.js";import{B as zt}from"./ButtonAugment-K-zrKZyw.js";import{B as Ct,b as Lt}from"./BaseTextInput-IcL3sG2L.js";var Tt=O("<!> <!> <!>",1),Ht=O('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function qt(J,t){var a;const P=F(t,["children","$$slots","$$events","$$legacy"]),A=F(P,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=g(),w=g(),L=g();let x,b=i(t,"defaultColor",8),D=i(t,"tooltip",24,()=>{}),B=i(t,"stateVariant",24,()=>{}),Q=i(t,"onClick",8),_=i(t,"tooltipDuration",8,1500),u=i(t,"icon",8,!1),K=i(t,"stickyColor",8,!0),X=i(t,"persistOnTooltipClose",8,!1),S=i(t,"tooltipNested",24,()=>{}),r=g("neutral"),z=g(b()),E=g(void 0),M=g((a=D())==null?void 0:a.neutral);async function V(o){var h;try{c(r,await Q()(o)??"neutral")}catch{c(r,"failure")}c(M,(h=D())==null?void 0:h[s(r)]),clearTimeout(x),x=setTimeout(()=>{var f;(f=s(E))==null||f(),K()||c(r,"neutral")},_())}q(()=>(s(y),s(w),R(A)),()=>{c(y,A.variant),c(w,rt(A,["variant"]))}),q(()=>(R(B()),s(r),s(y)),()=>{var o;c(L,((o=B())==null?void 0:o[s(r)])??s(y))}),q(()=>(s(r),R(b())),()=>{s(r)==="success"?c(z,"success"):s(r)==="failure"?c(z,"error"):c(z,b())}),st(),lt();var v=Ht(),T=Y(v);const n=vt(()=>(R(et),it(()=>[et.Hover])));bt(T,{onOpenChange:function(o){var h;X()||o||(clearTimeout(x),x=void 0,c(M,(h=D())==null?void 0:h.neutral),K()||c(r,"neutral"))},get content(){return s(M)},get triggerOn(){return s(n)},get nested(){return S()},get requestClose(){return s(E)},set requestClose(o){c(E,o)},children:(o,h)=>{var f=W(),Z=N(f),ct=I=>{wt(I,tt(()=>s(w),{get color(){return s(z)},get variant(){return s(L)},$$events:{click:V,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,j)=>{var d=Tt(),C=N(d);H(C,t,"iconLeft",{},null);var k=G(C,2);H(k,t,"default",{},null);var ut=G(k,2);H(ut,t,"iconRight",{},null),$(e,d)},$$slots:{default:!0}}))},ot=I=>{zt(I,tt(()=>s(w),{get color(){return s(z)},get variant(){return s(L)},$$events:{click:V,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,j)=>{var d=W(),C=N(d);H(C,t,"default",{},null),$(e,d)},$$slots:{default:!0,iconLeft:(e,j)=>{var d=W(),C=N(d);H(C,t,"iconLeft",{},null),$(e,d)},iconRight:(e,j)=>{var d=W(),C=N(d);H(C,t,"iconRight",{},null),$(e,d)}}}))};U(Z,I=>{u()?I(ct):I(ot,!1)}),$(o,f)},$$slots:{default:!0},$$legacy:!0}),$(J,v),nt()}var Rt=O('<label class="c-text-area-label svelte-c1sr7w"> </label>'),_t=O('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),Et=O("<textarea></textarea>"),It=O('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function Bt(J,t){const P=ht(t),A=F(t,["children","$$slots","$$events","$$legacy"]),y=F(A,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const w=g(),L=g(),x=g();let b=i(t,"label",24,()=>{}),D=i(t,"variant",8,"surface"),B=i(t,"size",8,2),Q=i(t,"color",24,()=>{}),_=i(t,"resize",8,"none"),u=i(t,"textInput",28,()=>{}),K=i(t,"type",8,"default"),X=i(t,"value",12,""),S=i(t,"id",24,()=>{});function r(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,T=Math.min(u().scrollHeight,v);u(u().style.height=`${T}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){r();const v=()=>r();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),q(()=>R(S()),()=>{c(w,S()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),q(()=>(s(L),s(x),R(y)),()=>{c(L,y.class),c(x,rt(y,["class"]))}),st(),lt();var z=It(),E=Y(z),M=v=>{var T=_t(),n=Y(T),a=h=>{var f=Rt(),Z=Y(f);$t(()=>{mt(f,"for",s(w)),yt(Z,b())}),$(h,f)};U(n,h=>{b()&&h(a)});var o=G(n,2);H(o,t,"topRightAction",{},null),$(v,T)};U(E,v=>{R(b()),it(()=>b()||P.topRightAction)&&v(M)});var V=G(E,2);Ct(V,{get type(){return K()},get variant(){return D()},get size(){return B()},get color(){return Q()},children:(v,T)=>{var n=Et();ft(n,a=>({id:s(w),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(L)}`,...s(x),[pt]:a}),[()=>({"c-textarea--resize-none":_()==="none","c-textarea--resize-both":_()==="both","c-textarea--resize-horizontal":_()==="horizontal","c-textarea--resize-vertical":_()==="vertical"})],"svelte-c1sr7w"),gt(n,a=>u(a),()=>u()),p(()=>Lt(n,X)),xt(n,a=>function(o){r();const h=()=>r();return o.addEventListener("input",h),setTimeout(r,0),{destroy(){o.removeEventListener("input",h)}}}(a)),p(()=>m("click",n,function(a){l.call(this,t,a)})),p(()=>m("focus",n,function(a){l.call(this,t,a)})),p(()=>m("keydown",n,function(a){l.call(this,t,a)})),p(()=>m("change",n,function(a){l.call(this,t,a)})),p(()=>m("input",n,function(a){l.call(this,t,a)})),p(()=>m("keyup",n,function(a){l.call(this,t,a)})),p(()=>m("blur",n,function(a){l.call(this,t,a)})),p(()=>m("select",n,function(a){l.call(this,t,a)})),p(()=>m("mouseup",n,function(a){l.call(this,t,a)})),$(v,n)},$$slots:{default:!0}}),$(J,z),nt()}export{qt as S,Bt as T};
