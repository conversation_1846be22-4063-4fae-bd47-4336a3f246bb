var ne=Object.defineProperty;var oe=(a,e,s)=>e in a?ne(a,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[e]=s;var f=(a,e,s)=>oe(a,typeof e!="symbol"?e+"":e,s);import{f as L,b as g,l as k,A as Q,u as ie,C as h,X as W,Y as z,m as B,Z as ce,K as D,_ as C,$ as X,D as le,J as I,a7 as re,F as de,G as J,N as ue,a3 as G,t as b,Q as H,ab as $,L as he,I as j,H as ge,M as Y,R as me,P as Z,W as K,w as pe,aA as A,a as N}from"./SpinnerAugment-AffdR7--.js";import{B as ve,a as we}from"./user-BlpcIo5U.js";import{C as fe,b as d,h as P}from"./IconButtonAugment-DVt24OaC.js";import{a as Ce}from"./BaseTextInput-IcL3sG2L.js";import{R as E}from"./message-broker-Bv_1VsFe.js";import{i as T}from"./index-CnLsnTY6.js";var ye=L('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function Ve(a){var e=ye();g(a,e)}var be=I("<div><!></div>");const Ie={Root:we,IconButton:function(a,e){const s=k(e,["children","$$slots","$$events","$$legacy"]),t=k(s,["color","highContrast","disabled"]);Q(e,!1);const c=B(),p=B(),r=ie(ve.CONTEXT_KEY);let u=h(e,"color",24,()=>{return n=r.color,l="neutral",typeof n=="string"&&["accent","neutral","error","success","warning","info"].includes(n)?n:l;var n,l}),o=h(e,"highContrast",8,!1),i=h(e,"disabled",8,!1),m=r.size===0?.5:r.size;W(()=>(C(c),C(p),D(t)),()=>{z(c,t.class),z(p,ce(t,["class"]))}),X(),le();var S=be(),_=b(S);const v=G(()=>`c-badge-icon-btn__base-btn ${C(c)}`);fe(_,re({get size(){return m},variant:"ghost",get color(){return u()},get highContrast(){return o()},get disabled(){return i()},get class(){return C(v)}},()=>C(p),{$$events:{click(n){d.call(this,e,n)},keyup(n){d.call(this,e,n)},keydown(n){d.call(this,e,n)},mousedown(n){d.call(this,e,n)},mouseover(n){d.call(this,e,n)},focus(n){d.call(this,e,n)},mouseleave(n){d.call(this,e,n)},blur(n){d.call(this,e,n)},contextmenu(n){d.call(this,e,n)}},children:(n,l)=>{var w=de(),x=J(w);ue(x,e,"default",{},null),g(n,w)},$$slots:{default:!0}})),H(()=>$(S,1,he(()=>`c-badge-icon-btn c-badge-icon-btn--${r.variant} c-badge-icon-btn--size-${m}`),"svelte-1im94um")),g(a,S),j()}};var Se=I("<span> </span> <span> </span>",1),xe=I('<label><!> <input type="checkbox" role="switch"/></label>');function Ne(a,e){Q(e,!1);const s=B();let t=h(e,"checked",12,!1),c=h(e,"disabled",8,!1),p=h(e,"size",8,2),r=h(e,"ariaLabel",24,()=>{}),u=h(e,"onText",24,()=>{}),o=h(e,"offText",24,()=>{});W(()=>(D(u()),D(o())),()=>{z(s,u()||o())}),X();var i=xe();let m;var S=b(i),_=l=>{var w=Se(),x=J(w);let U;var ee=b(x),O=Y(x,2);let R;var te=b(O);H((se,ae)=>{U=$(x,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,U,se),K(ee,o()||""),R=$(O,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,R,ae),K(te,u()||"")},[()=>({visible:!t()&&o()}),()=>({visible:t()&&u()})],G),g(l,w)};ge(S,l=>{C(s)&&l(_)});var v=Y(S,2);let n;H((l,w)=>{m=$(i,1,`c-toggle-track c-toggle-track-size--${p()??""}`,"svelte-xr5g0k",m,l),n=$(v,1,"c-toggle-input svelte-xr5g0k",null,n,w),v.disabled=c(),me(v,"aria-label",r())},[()=>({checked:t(),disabled:c(),"has-text":C(s)}),()=>({disabled:c()})],G),Ce(v,t),Z("keydown",v,function(l){c()||l.key!=="Enter"&&l.key!==" "||(l.preventDefault(),t(!t()))}),Z("change",i,function(l){d.call(this,e,l)}),g(a,i),j()}function Pe(a){const{rules:e,workspaceGuidelinesContent:s,contextRules:t=[]}=a,c=e.filter(o=>o.type===E.ALWAYS_ATTACHED).reduce((o,i)=>o+i.content.length+i.path.length,0),p=e.filter(o=>o.type===E.AGENT_REQUESTED).reduce((o,i)=>{var m;return o+100+(((m=i.description)==null?void 0:m.length)??0)+i.path.length},0),r=c+e.filter(o=>o.type===E.MANUAL).filter(o=>t.some(i=>i.path===o.path)).reduce((o,i)=>o+i.content.length+i.path.length,0)+p+s.length,u=a.rulesAndGuidelinesLimit&&r>a.rulesAndGuidelinesLimit;return{totalCharacterCount:r,isOverLimit:u,warningMessage:u&&a.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${r} chars)
        exceeds the limit of ${a.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}const M={enabled:!1,volume:.5},Ue={enabled:!0},$e=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var q=(a=>(a.AGENT_COMPLETE="agent-complete",a))(q||{});const ke={"agent-complete":$e},y=class y{constructor(){f(this,"audioCache",new Map)}static getInstance(){return y._instance||(y._instance=new y),y._instance}retrieveAudioElement(e,s){let t=this.audioCache.get(e);return t?t.volume=s.volume:(t=new Audio,t.src=ke[e],t.volume=s.volume,t.preload="auto",t._isUnlocked=!1,this.audioCache.set(e,t)),t}async playSound(e,s){if(s.enabled)try{const t=this.retrieveAudioElement(e,s);t.currentTime=0,await t.play()}catch(t){if(t instanceof DOMException&&t.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",t)}}async unlockSoundForConfig(e){if(!e.enabled)return;const s=this.retrieveAudioElement("agent-complete",e);if(!s._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),s._isUnlocked=!0}catch(t){console.warn("Failed to unlock sound:",t)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};f(y,"_instance");let V=y;const F=V.getInstance();class Le{constructor(e){f(this,"_soundSettings",pe(M));f(this,"_isLoaded",!1);f(this,"dispose",()=>{F.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:T.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){A(this._soundSettings).enabled&&F.unlockSoundForConfig(A(this._soundSettings))}async playAgentComplete(){const e=A(this._soundSettings);await F.playSound(q.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:T.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(M),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:T.updateSoundSettings,data:e}),this._soundSettings.update(s=>({...s,...e}))}catch(s){throw console.error("Failed to update sound settings:",s),s}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const s=Math.max(0,Math.min(1,e));await this.updateSettings({volume:s})}async resetToDefaults(){await this.updateSettings(M)}updateEnabled(e){this.setEnabled(e).catch(s=>{console.error("Failed to update enabled setting:",s)})}updateVolume(e){this.setVolume(e).catch(s=>{console.error("Failed to update volume setting:",s)})}}f(Le,"key","soundModel");var _e=L("<svg><!></svg>");function Oe(a,e){const s=k(e,["children","$$slots","$$events","$$legacy"]);var t=_e();N(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M384 336H192c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h140.1l67.9 67.9V320c0 8.8-7.2 16-16 16m-192 48h192c35.3 0 64-28.7 64-64V115.9c0-12.7-5.1-24.9-14.1-33.9l-67.8-67.9c-9-9-21.2-14.1-33.9-14.1H192c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64M64 128c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64h192c35.3 0 64-28.7 64-64v-32h-48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192c0-8.8 7.2-16 16-16h32v-48z"/>',!0),g(a,t)}var Ae=L("<svg><!></svg>");function Re(a,e){const s=k(e,["children","$$slots","$$events","$$legacy"]);var t=Ae();N(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),g(a,t)}var Ee=L('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function Ye(a){var e=Ee();g(a,e)}var Te=L("<svg><!></svg>");function Ze(a,e){const s=k(e,["children","$$slots","$$events","$$legacy"]);var t=Te();N(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),g(a,t)}export{Ie as B,Ve as C,Ue as D,Ye as G,Re as P,Le as S,Ne as T,Oe as a,Ze as b,Pe as c};
