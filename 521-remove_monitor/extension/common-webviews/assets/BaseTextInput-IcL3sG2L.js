import{i as y,ai as h,L as b,aj as m,A as z,C as i,X as $,$ as k,D as B,J as S,a as A,_ as C,a1 as E,m as j,T as D,F,G,N as I,b as p,t as J,I as K,Y as L,a0 as o,K as M}from"./SpinnerAugment-AffdR7--.js";import"./IconButtonAugment-DVt24OaC.js";function Y(a,e,l=e){var r=y();h(a,"input",t=>{var s=t?a.defaultValue:a.value;if(s=n(a)?d(s):s,l(s),r&&s!==(s=e())){var u=a.selectionStart,c=a.selectionEnd;a.value=s??"",c!==null&&(a.selectionStart=u,a.selectionEnd=Math.min(c,a.value.length))}}),b(e)==null&&a.value&&l(n(a)?d(a.value):a.value),m(()=>{var t=e();n(a)&&t===d(a.value)||(a.type!=="date"||t||a.value)&&t!==a.value&&(a.value=t??"")})}function _(a,e,l=e){h(a,"change",r=>{var t=r?a.defaultChecked:a.checked;l(t)}),b(e)==null&&l(a.checked),m(()=>{var r=e();a.checked=!!r})}function n(a){var e=a.type;return e==="number"||e==="range"}function d(a){return a===""?null:+a}var N=S("<div><!></div>");function q(a,e){z(e,!1);const l=j();let r=i(e,"variant",8,"surface"),t=i(e,"size",8,2),s=i(e,"type",8,"default"),u=i(e,"color",24,()=>{});$(()=>(M(u()),o),()=>{L(l,u()?o(u()):o("accent"))}),k(),B();var c=N();A(c,v=>({...C(l),class:`c-base-text-input c-base-text-input--${r()} c-base-text-input--size-${t()}`,[E]:v}),[()=>({"c-base-text-input--has-color":u()!==void 0})],"svelte-1mx5zy6");var x=J(c);D(x,{get type(){return s()},get size(){return t()},children:(v,T)=>{var f=F(),g=G(f);I(g,e,"default",{},null),p(v,f)},$$slots:{default:!0}}),p(a,c),K()}export{q as B,_ as a,Y as b};
