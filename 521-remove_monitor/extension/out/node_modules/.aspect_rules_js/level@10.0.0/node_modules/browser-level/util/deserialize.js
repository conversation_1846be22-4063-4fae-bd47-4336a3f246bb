'use strict'

const textEncoder = new TextEncoder()

module.exports = function (data) {
  if (data === undefined) {
    // Undefined means not found in both IndexedDB and AbstractLevel
    return data
  } else if (data instanceof Uint8Array) {
    return data
  } else if (data instanceof ArrayBuffer) {
    return new Uint8Array(data)
  } else {
    // Non-binary data stored with an old version (level-js < 5.0.0)
    return textEncoder.encode(data)
  }
}
